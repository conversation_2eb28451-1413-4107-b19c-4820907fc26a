[{"C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx": "1", "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx": "2", "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx": "3", "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx": "4", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx": "5", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx": "6", "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx": "7", "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx": "8", "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts": "9", "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx": "10", "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx": "11", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx": "12", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx": "13", "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx": "14", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx": "15", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx": "16", "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx": "17", "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx": "18", "C:\\web-app\\dukancard-app\\src\\components\\common\\LoadingOverlay.tsx": "19", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\BusinessCard.tsx": "20", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CategorySelector.tsx": "21", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CompactLocationPicker.tsx": "22", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\DiscoverySkeletons.tsx": "23", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ErrorComponents.tsx": "24", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\FullScreenLocationSelector.tsx": "25", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\NavigationHandlers.tsx": "26", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ResultsList.tsx": "27", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SearchSection.tsx": "28", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SortBottomSheet.tsx": "29", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\CompactLocationPickerStyles.ts": "30", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\DiscoverScreenStyles.ts": "31", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\LocationSelectorScreenStyles.ts": "32", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\ResultsListStyles.ts": "33", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ViewToggle.tsx": "34", "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx": "35", "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx": "36", "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts": "37", "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts": "38", "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts": "39", "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts": "40", "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts": "41", "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts": "42", "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts": "43", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx": "44", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx": "45", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx": "46", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx": "47", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx": "48", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx": "49", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx": "50", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx": "51", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx": "52", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx": "53", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx": "54", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx": "55", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx": "56", "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx": "57", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx": "58", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx": "59", "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx": "60", "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx": "61", "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx": "62", "C:\\web-app\\dukancard-app\\src\\components\\index.ts": "63", "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx": "64", "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx": "65", "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts": "66", "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx": "67", "C:\\web-app\\dukancard-app\\src\\components\\layout\\SafeAreaWrapper.tsx": "68", "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx": "69", "C:\\web-app\\dukancard-app\\src\\components\\layout\\StatusBarManager.tsx": "70", "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx": "71", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessFollowersModal.tsx": "72", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessLikesModal.tsx": "73", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowersList.tsx": "74", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesList.tsx": "75", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductForm.tsx": "76", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsList.tsx": "77", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsSortBottomSheet.tsx": "78", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantForm.tsx": "79", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantList.tsx": "80", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageCardModal.tsx": "81", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageProductsModal.tsx": "82", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AdvancedFeaturesSection.tsx": "83", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AppearanceSection.tsx": "84", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BasicInfoSection.tsx": "85", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BusinessDetailsSection.tsx": "86", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\ContactLocationSection.tsx": "87", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\SocialLinksSection.tsx": "88", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\StatusSettingsSection.tsx": "89", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\VariantModal.tsx": "90", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\FollowingList.tsx": "91", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\LikesList.tsx": "92", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsList.tsx": "93", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsSortBottomSheet.tsx": "94", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\EditProfileModal.tsx": "95", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\FollowingModal.tsx": "96", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\LikesModal.tsx": "97", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\ReviewsModal.tsx": "98", "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx": "99", "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx": "100", "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx": "101", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx": "102", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ColorPickerBottomSheet.tsx": "103", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx": "104", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx": "105", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\VariantTypeBottomSheet.tsx": "106", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx": "107", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx": "108", "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx": "109", "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx": "110", "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx": "111", "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx": "112", "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx": "113", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx": "114", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx": "115", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressInformationSection.tsx": "116", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx": "117", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadSection.tsx": "118", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx": "119", "C:\\web-app\\dukancard-app\\src\\components\\profile\\PersonalInformationSection.tsx": "120", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx": "121", "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx": "122", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx": "123", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx": "124", "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx": "125", "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx": "126", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx": "127", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx": "128", "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx": "129", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx": "130", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx": "131", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx": "132", "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx": "133", "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx": "134", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx": "135", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx": "136", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts": "137", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx": "138", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx": "139", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx": "140", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\FollowingModalSkeleton.tsx": "141", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\LikesModalSkeleton.tsx": "142", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ProductsModalSkeleton.tsx": "143", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ReviewsModalSkeleton.tsx": "144", "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx": "145", "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx": "146", "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx": "147", "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx": "148", "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx": "149", "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx": "150", "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx": "151", "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx": "152", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx": "153", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx": "154", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx": "155", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx": "156", "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts": "157", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx": "158", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx": "159", "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx": "160", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx": "161", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx": "162", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx": "163", "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts": "164", "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx": "165", "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts": "166", "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx": "167", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx": "168", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx": "169", "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts": "170", "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx": "171", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx": "172", "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts": "173", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx": "174", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx": "175", "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts": "176", "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts": "177", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx": "178", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx": "179", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx": "180", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx": "181", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx": "182", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx": "183", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx": "184", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx": "185", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx": "186", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx": "187", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx": "188", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx": "189", "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts": "190", "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts": "191", "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts": "192", "C:\\web-app\\dukancard-app\\src\\constants\\predefinedVariants.ts": "193", "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx": "194", "C:\\web-app\\dukancard-app\\src\\contexts\\DiscoveryContext.tsx": "195", "C:\\web-app\\dukancard-app\\src\\contexts\\LocationContext.tsx": "196", "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx": "197", "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx": "198", "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx": "199", "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts": "200", "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts": "201", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts": "202", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts": "203", "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts": "204", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts": "205", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts": "206", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts": "207", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts": "208", "C:\\web-app\\dukancard-app\\src\\hooks\\useDebounce.ts": "209", "C:\\web-app\\dukancard-app\\src\\hooks\\useDynamicSafeArea.ts": "210", "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts": "211", "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts": "212", "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts": "213", "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts": "214", "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts": "215", "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts": "216", "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts": "217", "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts": "218", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\businessActions.ts": "219", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\DiscoveryService.ts": "220", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\index.ts": "221", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\locationActions.ts": "222", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\productActions.ts": "223", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\types.ts": "224", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\locationUtils.ts": "225", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\secureBusinessProfiles.ts": "226", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\sortMappings.ts": "227", "C:\\web-app\\dukancard-app\\src\\services\\locationStorageService.ts": "228", "C:\\web-app\\dukancard-app\\src\\types\\ad.ts": "229", "C:\\web-app\\dukancard-app\\src\\types\\auth.ts": "230", "C:\\web-app\\dukancard-app\\src\\types\\business\\analytics.ts": "231", "C:\\web-app\\dukancard-app\\src\\types\\components.ts": "232", "C:\\web-app\\dukancard-app\\src\\types\\discovery.ts": "233", "C:\\web-app\\dukancard-app\\src\\types\\index.ts": "234", "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts": "235", "C:\\web-app\\dukancard-app\\src\\types\\profile.ts": "236", "C:\\web-app\\dukancard-app\\src\\types\\screens.ts": "237", "C:\\web-app\\dukancard-app\\src\\types\\ui.ts": "238", "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts": "239", "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts": "240", "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts": "241", "C:\\web-app\\dukancard-app\\src\\utils\\distanceCalculation.ts": "242", "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts": "243", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts": "244", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts": "245", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "246", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts": "247", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts": "248", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts": "249", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts": "250", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts": "251", "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts": "252", "C:\\web-app\\dukancard-app\\src\\utils\\imageCompression.ts": "253", "C:\\web-app\\dukancard-app\\src\\utils\\index.ts": "254", "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts": "255", "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts": "256", "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts": "257", "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts": "258", "C:\\web-app\\dukancard-app\\src\\utils\\sortMappings.ts": "259", "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts": "260", "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts": "261", "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts": "262", "C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx": "263", "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx": "264", "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx": "265", "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx": "266", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx": "267", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx": "268", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx": "269", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx": "270", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx": "271", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx": "272", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx": "273", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx": "274", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx": "275", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx": "276", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx": "277", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx": "278", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx": "279", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "280", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx": "281", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx": "282", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx": "283", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx": "284", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx": "285", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx": "286", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx": "287", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx": "288", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx": "289", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx": "290", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx": "291", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx": "292", "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx": "293", "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx": "294", "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx": "295", "C:\\web-app\\dukancard-app\\app\\+not-found.tsx": "296", "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx": "297", "C:\\web-app\\dukancard-app\\app\\index.tsx": "298", "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx": "299", "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx": "300", "C:\\web-app\\dukancard-app\\app\\_layout.tsx": "301", "C:\\web-app\\dukancard-app\\src\\utils\\storage-paths.ts": "302", "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step1AvatarName.tsx": "303", "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step2AddressLocation.tsx": "304", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessDeleteAccountModal.tsx": "305", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessStatusSettingsModal.tsx": "306", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\GalleryModal.tsx": "307", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ShareBusinessCardModal.tsx": "308", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.styles.ts": "309", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.tsx": "310", "C:\\web-app\\dukancard-app\\src\\types\\gallery.ts": "311", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessReviewsModal.tsx": "312", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowingList.tsx": "313", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesGivenList.tsx": "314", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsGivenList.tsx": "315", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsList.tsx": "316", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessFollowersModalSkeleton.tsx": "317", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessLikesModalSkeleton.tsx": "318", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessReviewsModalSkeleton.tsx": "319", "C:\\web-app\\dukancard-app\\src\\config\\supabase\\constants.ts": "320", "C:\\web-app\\dukancard-app\\src\\types\\business.ts": "321", "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts": "322"}, {"size": 6700, "mtime": 1752078878041, "results": "323", "hashOfConfig": "324"}, {"size": 2348, "mtime": 1752078878041, "results": "325", "hashOfConfig": "324"}, {"size": 11448, "mtime": 1753463799164, "results": "326", "hashOfConfig": "324"}, {"size": 7406, "mtime": 1752078878041, "results": "327", "hashOfConfig": "324"}, {"size": 3642, "mtime": 1752843691709, "results": "328", "hashOfConfig": "324"}, {"size": 9032, "mtime": 1753101911257, "results": "329", "hashOfConfig": "324"}, {"size": 9530, "mtime": 1753452361866, "results": "330", "hashOfConfig": "324"}, {"size": 1271, "mtime": 1753462862423, "results": "331", "hashOfConfig": "324"}, {"size": 966, "mtime": 1752078878057, "results": "332", "hashOfConfig": "324"}, {"size": 11914, "mtime": 1752078878057, "results": "333", "hashOfConfig": "324"}, {"size": 9940, "mtime": 1753101911257, "results": "334", "hashOfConfig": "324"}, {"size": 6948, "mtime": 1753101911257, "results": "335", "hashOfConfig": "324"}, {"size": 10337, "mtime": 1753480242527, "results": "336", "hashOfConfig": "324"}, {"size": 6195, "mtime": 1752078878057, "results": "337", "hashOfConfig": "324"}, {"size": 8770, "mtime": 1752078878057, "results": "338", "hashOfConfig": "324"}, {"size": 16168, "mtime": 1753480496292, "results": "339", "hashOfConfig": "324"}, {"size": 2174, "mtime": 1752078878057, "results": "340", "hashOfConfig": "324"}, {"size": 1283, "mtime": 1752078878041, "results": "341", "hashOfConfig": "324"}, {"size": 1368, "mtime": 1752677492916, "results": "342", "hashOfConfig": "324"}, {"size": 8520, "mtime": 1753101911257, "results": "343", "hashOfConfig": "324"}, {"size": 4430, "mtime": 1752078878057, "results": "344", "hashOfConfig": "324"}, {"size": 4617, "mtime": 1752078878073, "results": "345", "hashOfConfig": "324"}, {"size": 11891, "mtime": 1752078878073, "results": "346", "hashOfConfig": "324"}, {"size": 10501, "mtime": 1752078878073, "results": "347", "hashOfConfig": "324"}, {"size": 24989, "mtime": 1753436913440, "results": "348", "hashOfConfig": "324"}, {"size": 6509, "mtime": 1753462415834, "results": "349", "hashOfConfig": "324"}, {"size": 9389, "mtime": 1753462468689, "results": "350", "hashOfConfig": "324"}, {"size": 4869, "mtime": 1752078878073, "results": "351", "hashOfConfig": "324"}, {"size": 8673, "mtime": 1752078878073, "results": "352", "hashOfConfig": "324"}, {"size": 1130, "mtime": 1752078878073, "results": "353", "hashOfConfig": "324"}, {"size": 942, "mtime": 1752078878073, "results": "354", "hashOfConfig": "324"}, {"size": 3399, "mtime": 1752078878073, "results": "355", "hashOfConfig": "324"}, {"size": 919, "mtime": 1752078878073, "results": "356", "hashOfConfig": "324"}, {"size": 4801, "mtime": 1752078878073, "results": "357", "hashOfConfig": "324"}, {"size": 6175, "mtime": 1752078878041, "results": "358", "hashOfConfig": "324"}, {"size": 737, "mtime": 1752078878041, "results": "359", "hashOfConfig": "324"}, {"size": 203, "mtime": 1752078878088, "results": "360", "hashOfConfig": "324"}, {"size": 170, "mtime": 1752078878088, "results": "361", "hashOfConfig": "324"}, {"size": 203, "mtime": 1752078878088, "results": "362", "hashOfConfig": "324"}, {"size": 416, "mtime": 1752078878088, "results": "363", "hashOfConfig": "324"}, {"size": 246, "mtime": 1752078878088, "results": "364", "hashOfConfig": "324"}, {"size": 226, "mtime": 1752078878088, "results": "365", "hashOfConfig": "324"}, {"size": 219, "mtime": 1752078878104, "results": "366", "hashOfConfig": "324"}, {"size": 3552, "mtime": 1753436913440, "results": "367", "hashOfConfig": "324"}, {"size": 16773, "mtime": 1753436913440, "results": "368", "hashOfConfig": "324"}, {"size": 19418, "mtime": 1753436913446, "results": "369", "hashOfConfig": "324"}, {"size": 3413, "mtime": 1753436913447, "results": "370", "hashOfConfig": "324"}, {"size": 11854, "mtime": 1753436913449, "results": "371", "hashOfConfig": "324"}, {"size": 14855, "mtime": 1753436913449, "results": "372", "hashOfConfig": "324"}, {"size": 4745, "mtime": 1752078878119, "results": "373", "hashOfConfig": "324"}, {"size": 853, "mtime": 1752078878119, "results": "374", "hashOfConfig": "324"}, {"size": 26686, "mtime": 1752683463743, "results": "375", "hashOfConfig": "324"}, {"size": 10083, "mtime": 1752078878119, "results": "376", "hashOfConfig": "324"}, {"size": 8134, "mtime": 1752078878135, "results": "377", "hashOfConfig": "324"}, {"size": 17474, "mtime": 1752078878135, "results": "378", "hashOfConfig": "324"}, {"size": 11033, "mtime": 1752078878135, "results": "379", "hashOfConfig": "324"}, {"size": 15951, "mtime": 1753436913449, "results": "380", "hashOfConfig": "324"}, {"size": 2781, "mtime": 1752078878142, "results": "381", "hashOfConfig": "324"}, {"size": 3905, "mtime": 1752078878142, "results": "382", "hashOfConfig": "324"}, {"size": 582, "mtime": 1752078878041, "results": "383", "hashOfConfig": "324"}, {"size": 859, "mtime": 1752078878041, "results": "384", "hashOfConfig": "324"}, {"size": 1509, "mtime": 1752078878142, "results": "385", "hashOfConfig": "324"}, {"size": 1485, "mtime": 1752078878142, "results": "386", "hashOfConfig": "324"}, {"size": 5700, "mtime": 1752078878151, "results": "387", "hashOfConfig": "324"}, {"size": 9064, "mtime": 1752652596805, "results": "388", "hashOfConfig": "324"}, {"size": 622, "mtime": 1752078878168, "results": "389", "hashOfConfig": "324"}, {"size": 8493, "mtime": 1752078878151, "results": "390", "hashOfConfig": "324"}, {"size": 2168, "mtime": 1752078878151, "results": "391", "hashOfConfig": "324"}, {"size": 5309, "mtime": 1753452545870, "results": "392", "hashOfConfig": "324"}, {"size": 1110, "mtime": 1753452761979, "results": "393", "hashOfConfig": "324"}, {"size": 8171, "mtime": 1752078878168, "results": "394", "hashOfConfig": "324"}, {"size": 5648, "mtime": 1753480143662, "results": "395", "hashOfConfig": "324"}, {"size": 5403, "mtime": 1753480079226, "results": "396", "hashOfConfig": "324"}, {"size": 5964, "mtime": 1753480581026, "results": "397", "hashOfConfig": "324"}, {"size": 5426, "mtime": 1753480627651, "results": "398", "hashOfConfig": "324"}, {"size": 34339, "mtime": 1753101911271, "results": "399", "hashOfConfig": "324"}, {"size": 12556, "mtime": 1753101911271, "results": "400", "hashOfConfig": "324"}, {"size": 7541, "mtime": 1752083287996, "results": "401", "hashOfConfig": "324"}, {"size": 29492, "mtime": 1753464124502, "results": "402", "hashOfConfig": "324"}, {"size": 8103, "mtime": 1753101911271, "results": "403", "hashOfConfig": "324"}, {"size": 8217, "mtime": 1752772689951, "results": "404", "hashOfConfig": "324"}, {"size": 10266, "mtime": 1753101911271, "results": "405", "hashOfConfig": "324"}, {"size": 9194, "mtime": 1752773304153, "results": "406", "hashOfConfig": "324"}, {"size": 9143, "mtime": 1752773218942, "results": "407", "hashOfConfig": "324"}, {"size": 11142, "mtime": 1752772799158, "results": "408", "hashOfConfig": "324"}, {"size": 11553, "mtime": 1752773033163, "results": "409", "hashOfConfig": "324"}, {"size": 13549, "mtime": 1752774443482, "results": "410", "hashOfConfig": "324"}, {"size": 9759, "mtime": 1752773120405, "results": "411", "hashOfConfig": "324"}, {"size": 8299, "mtime": 1752772955122, "results": "412", "hashOfConfig": "324"}, {"size": 1368, "mtime": 1753101911271, "results": "413", "hashOfConfig": "324"}, {"size": 4749, "mtime": 1753436913462, "results": "414", "hashOfConfig": "324"}, {"size": 3685, "mtime": 1753436913462, "results": "415", "hashOfConfig": "324"}, {"size": 5066, "mtime": 1753436185890, "results": "416", "hashOfConfig": "324"}, {"size": 5891, "mtime": 1753436185891, "results": "417", "hashOfConfig": "324"}, {"size": 21655, "mtime": 1753436185886, "results": "418", "hashOfConfig": "324"}, {"size": 4008, "mtime": 1753436185887, "results": "419", "hashOfConfig": "324"}, {"size": 3663, "mtime": 1753436913449, "results": "420", "hashOfConfig": "324"}, {"size": 5533, "mtime": 1753436913462, "results": "421", "hashOfConfig": "324"}, {"size": 10941, "mtime": 1753436913462, "results": "422", "hashOfConfig": "324"}, {"size": 2793, "mtime": 1752078878247, "results": "423", "hashOfConfig": "324"}, {"size": 2106, "mtime": 1752078878041, "results": "424", "hashOfConfig": "324"}, {"size": 11262, "mtime": 1752078878247, "results": "425", "hashOfConfig": "324"}, {"size": 6314, "mtime": 1752164352608, "results": "426", "hashOfConfig": "324"}, {"size": 6366, "mtime": 1752078878247, "results": "427", "hashOfConfig": "324"}, {"size": 9336, "mtime": 1752771772861, "results": "428", "hashOfConfig": "324"}, {"size": 5984, "mtime": 1752164382084, "results": "429", "hashOfConfig": "324"}, {"size": 4230, "mtime": 1752078878247, "results": "430", "hashOfConfig": "324"}, {"size": 2684, "mtime": 1752078878247, "results": "431", "hashOfConfig": "324"}, {"size": 10226, "mtime": 1752078878247, "results": "432", "hashOfConfig": "324"}, {"size": 1575, "mtime": 1752078878247, "results": "433", "hashOfConfig": "324"}, {"size": 9527, "mtime": 1752078878247, "results": "434", "hashOfConfig": "324"}, {"size": 5403, "mtime": 1753072097102, "results": "435", "hashOfConfig": "324"}, {"size": 11655, "mtime": 1752078878247, "results": "436", "hashOfConfig": "324"}, {"size": 11518, "mtime": 1753101911271, "results": "437", "hashOfConfig": "324"}, {"size": 13102, "mtime": 1752078878247, "results": "438", "hashOfConfig": "324"}, {"size": 11037, "mtime": 1752078878262, "results": "439", "hashOfConfig": "324"}, {"size": 8857, "mtime": 1752078878262, "results": "440", "hashOfConfig": "324"}, {"size": 6245, "mtime": 1752678857829, "results": "441", "hashOfConfig": "324"}, {"size": 7340, "mtime": 1752078878262, "results": "442", "hashOfConfig": "324"}, {"size": 1884, "mtime": 1752654352007, "results": "443", "hashOfConfig": "324"}, {"size": 5037, "mtime": 1752078878262, "results": "444", "hashOfConfig": "324"}, {"size": 2500, "mtime": 1752078878262, "results": "445", "hashOfConfig": "324"}, {"size": 9974, "mtime": 1752078878262, "results": "446", "hashOfConfig": "324"}, {"size": 16789, "mtime": 1752078878262, "results": "447", "hashOfConfig": "324"}, {"size": 6697, "mtime": 1752078878262, "results": "448", "hashOfConfig": "324"}, {"size": 12415, "mtime": 1752078878262, "results": "449", "hashOfConfig": "324"}, {"size": 12074, "mtime": 1752078878262, "results": "450", "hashOfConfig": "324"}, {"size": 14058, "mtime": 1752078878278, "results": "451", "hashOfConfig": "324"}, {"size": 6205, "mtime": 1753452622105, "results": "452", "hashOfConfig": "324"}, {"size": 4276, "mtime": 1752078878278, "results": "453", "hashOfConfig": "324"}, {"size": 3025, "mtime": 1752078878278, "results": "454", "hashOfConfig": "324"}, {"size": 4287, "mtime": 1752078878278, "results": "455", "hashOfConfig": "324"}, {"size": 2417, "mtime": 1752078878278, "results": "456", "hashOfConfig": "324"}, {"size": 17959, "mtime": 1752078878278, "results": "457", "hashOfConfig": "324"}, {"size": 2325, "mtime": 1752078878278, "results": "458", "hashOfConfig": "324"}, {"size": 3529, "mtime": 1752078878278, "results": "459", "hashOfConfig": "324"}, {"size": 135, "mtime": 1752078878278, "results": "460", "hashOfConfig": "324"}, {"size": 2012, "mtime": 1753452375303, "results": "461", "hashOfConfig": "324"}, {"size": 9837, "mtime": 1753072138887, "results": "462", "hashOfConfig": "324"}, {"size": 1300, "mtime": 1752078878278, "results": "463", "hashOfConfig": "324"}, {"size": 2137, "mtime": 1752078878293, "results": "464", "hashOfConfig": "324"}, {"size": 2044, "mtime": 1752078878293, "results": "465", "hashOfConfig": "324"}, {"size": 4100, "mtime": 1752088071254, "results": "466", "hashOfConfig": "324"}, {"size": 2897, "mtime": 1752078878293, "results": "467", "hashOfConfig": "324"}, {"size": 4752, "mtime": 1752925041796, "results": "468", "hashOfConfig": "324"}, {"size": 8720, "mtime": 1752925041797, "results": "469", "hashOfConfig": "324"}, {"size": 2505, "mtime": 1752078878293, "results": "470", "hashOfConfig": "324"}, {"size": 6483, "mtime": 1752078878293, "results": "471", "hashOfConfig": "324"}, {"size": 4310, "mtime": 1752078878293, "results": "472", "hashOfConfig": "324"}, {"size": 4919, "mtime": 1752925041797, "results": "473", "hashOfConfig": "324"}, {"size": 803, "mtime": 1752078878041, "results": "474", "hashOfConfig": "324"}, {"size": 486, "mtime": 1752078878041, "results": "475", "hashOfConfig": "324"}, {"size": 8070, "mtime": 1753452392438, "results": "476", "hashOfConfig": "324"}, {"size": 1165, "mtime": 1752078878293, "results": "477", "hashOfConfig": "324"}, {"size": 8019, "mtime": 1752078878309, "results": "478", "hashOfConfig": "324"}, {"size": 2681, "mtime": 1752508805201, "results": "479", "hashOfConfig": "324"}, {"size": 146, "mtime": 1752078878342, "results": "480", "hashOfConfig": "324"}, {"size": 3070, "mtime": 1752078878309, "results": "481", "hashOfConfig": "324"}, {"size": 6869, "mtime": 1752078878325, "results": "482", "hashOfConfig": "324"}, {"size": 2437, "mtime": 1752509552039, "results": "483", "hashOfConfig": "324"}, {"size": 6521, "mtime": 1752078878325, "results": "484", "hashOfConfig": "324"}, {"size": 8512, "mtime": 1752078878325, "results": "485", "hashOfConfig": "324"}, {"size": 6960, "mtime": 1752078878325, "results": "486", "hashOfConfig": "324"}, {"size": 191, "mtime": 1752078878342, "results": "487", "hashOfConfig": "324"}, {"size": 8704, "mtime": 1752078878325, "results": "488", "hashOfConfig": "324"}, {"size": 154, "mtime": 1752078878342, "results": "489", "hashOfConfig": "324"}, {"size": 1212, "mtime": 1752509267083, "results": "490", "hashOfConfig": "324"}, {"size": 630, "mtime": 1752078878325, "results": "491", "hashOfConfig": "324"}, {"size": 1463, "mtime": 1752078878325, "results": "492", "hashOfConfig": "324"}, {"size": 164, "mtime": 1752078878342, "results": "493", "hashOfConfig": "324"}, {"size": 5488, "mtime": 1752078878325, "results": "494", "hashOfConfig": "324"}, {"size": 5597, "mtime": 1752673608938, "results": "495", "hashOfConfig": "324"}, {"size": 182, "mtime": 1752078878356, "results": "496", "hashOfConfig": "324"}, {"size": 4297, "mtime": 1753436913462, "results": "497", "hashOfConfig": "324"}, {"size": 4954, "mtime": 1752078878325, "results": "498", "hashOfConfig": "324"}, {"size": 103, "mtime": 1752078878356, "results": "499", "hashOfConfig": "324"}, {"size": 118, "mtime": 1752078878356, "results": "500", "hashOfConfig": "324"}, {"size": 2218, "mtime": 1752078878340, "results": "501", "hashOfConfig": "324"}, {"size": 5642, "mtime": 1752769171557, "results": "502", "hashOfConfig": "324"}, {"size": 6418, "mtime": 1752078878340, "results": "503", "hashOfConfig": "324"}, {"size": 4561, "mtime": 1752078878342, "results": "504", "hashOfConfig": "324"}, {"size": 3124, "mtime": 1752078878342, "results": "505", "hashOfConfig": "324"}, {"size": 3747, "mtime": 1752228732395, "results": "506", "hashOfConfig": "324"}, {"size": 17543, "mtime": 1752761198324, "results": "507", "hashOfConfig": "324"}, {"size": 2219, "mtime": 1752078878342, "results": "508", "hashOfConfig": "324"}, {"size": 566, "mtime": 1752078878342, "results": "509", "hashOfConfig": "324"}, {"size": 165, "mtime": 1752078878342, "results": "510", "hashOfConfig": "324"}, {"size": 5248, "mtime": 1752078878342, "results": "511", "hashOfConfig": "324"}, {"size": 8082, "mtime": 1752078878342, "results": "512", "hashOfConfig": "324"}, {"size": 2617, "mtime": 1752078878360, "results": "513", "hashOfConfig": "324"}, {"size": 962, "mtime": 1752078878360, "results": "514", "hashOfConfig": "324"}, {"size": 3021, "mtime": 1752078878360, "results": "515", "hashOfConfig": "324"}, {"size": 17737, "mtime": 1752155050122, "results": "516", "hashOfConfig": "324"}, {"size": 11758, "mtime": 1753436913472, "results": "517", "hashOfConfig": "324"}, {"size": 20619, "mtime": 1753463191735, "results": "518", "hashOfConfig": "324"}, {"size": 5076, "mtime": 1753452257285, "results": "519", "hashOfConfig": "324"}, {"size": 7415, "mtime": 1752078878360, "results": "520", "hashOfConfig": "324"}, {"size": 8309, "mtime": 1752078878360, "results": "521", "hashOfConfig": "324"}, {"size": 3053, "mtime": 1752078878360, "results": "522", "hashOfConfig": "324"}, {"size": 1882, "mtime": 1752078878373, "results": "523", "hashOfConfig": "324"}, {"size": 4833, "mtime": 1752078878373, "results": "524", "hashOfConfig": "324"}, {"size": 8177, "mtime": 1753101911271, "results": "525", "hashOfConfig": "324"}, {"size": 2162, "mtime": 1752078878373, "results": "526", "hashOfConfig": "324"}, {"size": 4524, "mtime": 1752078878373, "results": "527", "hashOfConfig": "324"}, {"size": 8324, "mtime": 1753480532798, "results": "528", "hashOfConfig": "324"}, {"size": 4309, "mtime": 1753436913475, "results": "529", "hashOfConfig": "324"}, {"size": 224, "mtime": 1752078878373, "results": "530", "hashOfConfig": "324"}, {"size": 501, "mtime": 1752078878373, "results": "531", "hashOfConfig": "324"}, {"size": 1272, "mtime": 1752078878373, "results": "532", "hashOfConfig": "324"}, {"size": 1703, "mtime": 1752078878373, "results": "533", "hashOfConfig": "324"}, {"size": 4891, "mtime": 1752078878373, "results": "534", "hashOfConfig": "324"}, {"size": 1849, "mtime": 1752078878388, "results": "535", "hashOfConfig": "324"}, {"size": 3652, "mtime": 1753436913476, "results": "536", "hashOfConfig": "324"}, {"size": 2447, "mtime": 1753436913476, "results": "537", "hashOfConfig": "324"}, {"size": 2412, "mtime": 1752078878388, "results": "538", "hashOfConfig": "324"}, {"size": 3949, "mtime": 1752078878388, "results": "539", "hashOfConfig": "324"}, {"size": 2356, "mtime": 1752078878388, "results": "540", "hashOfConfig": "324"}, {"size": 565, "mtime": 1752078878388, "results": "541", "hashOfConfig": "324"}, {"size": 6189, "mtime": 1753464183454, "results": "542", "hashOfConfig": "324"}, {"size": 17713, "mtime": 1753479243478, "results": "543", "hashOfConfig": "324"}, {"size": 170, "mtime": 1752078878388, "results": "544", "hashOfConfig": "324"}, {"size": 22413, "mtime": 1753479696083, "results": "545", "hashOfConfig": "324"}, {"size": 14946, "mtime": 1753479752891, "results": "546", "hashOfConfig": "324"}, {"size": 2980, "mtime": 1753463285109, "results": "547", "hashOfConfig": "324"}, {"size": 1671, "mtime": 1752078878404, "results": "548", "hashOfConfig": "324"}, {"size": 13486, "mtime": 1753480559984, "results": "549", "hashOfConfig": "324"}, {"size": 0, "mtime": 1752078878404, "results": "550", "hashOfConfig": "324"}, {"size": 12996, "mtime": 1752078878404, "results": "551", "hashOfConfig": "324"}, {"size": 410, "mtime": 1752078878404, "results": "552", "hashOfConfig": "324"}, {"size": 5581, "mtime": 1753072256601, "results": "553", "hashOfConfig": "324"}, {"size": 902, "mtime": 1752078878404, "results": "554", "hashOfConfig": "324"}, {"size": 393, "mtime": 1752078878404, "results": "555", "hashOfConfig": "324"}, {"size": 7463, "mtime": 1753480267035, "results": "556", "hashOfConfig": "324"}, {"size": 315, "mtime": 1752078878421, "results": "557", "hashOfConfig": "324"}, {"size": 351, "mtime": 1752078878421, "results": "558", "hashOfConfig": "324"}, {"size": 462, "mtime": 1752078878421, "results": "559", "hashOfConfig": "324"}, {"size": 303, "mtime": 1752078878421, "results": "560", "hashOfConfig": "324"}, {"size": 422, "mtime": 1752078878421, "results": "561", "hashOfConfig": "324"}, {"size": 4903, "mtime": 1753436913484, "results": "562", "hashOfConfig": "324"}, {"size": 8418, "mtime": 1752078878421, "results": "563", "hashOfConfig": "324"}, {"size": 4141, "mtime": 1753436913484, "results": "564", "hashOfConfig": "324"}, {"size": 4768, "mtime": 1752078878421, "results": "565", "hashOfConfig": "324"}, {"size": 12056, "mtime": 1753069833441, "results": "566", "hashOfConfig": "324"}, {"size": 3715, "mtime": 1752078878436, "results": "567", "hashOfConfig": "324"}, {"size": 5950, "mtime": 1752929862955, "results": "568", "hashOfConfig": "324"}, {"size": 9417, "mtime": 1752929450701, "results": "569", "hashOfConfig": "324"}, {"size": 141, "mtime": 1752078878442, "results": "570", "hashOfConfig": "324"}, {"size": 10403, "mtime": 1752078878442, "results": "571", "hashOfConfig": "324"}, {"size": 4149, "mtime": 1752078878442, "results": "572", "hashOfConfig": "324"}, {"size": 7483, "mtime": 1752078878442, "results": "573", "hashOfConfig": "324"}, {"size": 5633, "mtime": 1752078878442, "results": "574", "hashOfConfig": "324"}, {"size": 2746, "mtime": 1752078878442, "results": "575", "hashOfConfig": "324"}, {"size": 8387, "mtime": 1752155050123, "results": "576", "hashOfConfig": "324"}, {"size": 732, "mtime": 1752078878442, "results": "577", "hashOfConfig": "324"}, {"size": 6262, "mtime": 1752078878452, "results": "578", "hashOfConfig": "324"}, {"size": 1732, "mtime": 1753452149675, "results": "579", "hashOfConfig": "324"}, {"size": 3162, "mtime": 1752078878452, "results": "580", "hashOfConfig": "324"}, {"size": 4293, "mtime": 1752078878452, "results": "581", "hashOfConfig": "324"}, {"size": 1829, "mtime": 1752078878452, "results": "582", "hashOfConfig": "324"}, {"size": 1339, "mtime": 1752078878452, "results": "583", "hashOfConfig": "324"}, {"size": 2395, "mtime": 1753072301980, "results": "584", "hashOfConfig": "324"}, {"size": 7446, "mtime": 1752859256978, "results": "585", "hashOfConfig": "324"}, {"size": 10723, "mtime": 1752435112746, "results": "586", "hashOfConfig": "324"}, {"size": 25093, "mtime": 1753436913385, "results": "587", "hashOfConfig": "324"}, {"size": 26307, "mtime": 1753452543994, "results": "588", "hashOfConfig": "324"}, {"size": 2617, "mtime": 1753436913369, "results": "589", "hashOfConfig": "324"}, {"size": 2092, "mtime": 1752078877815, "results": "590", "hashOfConfig": "324"}, {"size": 3430, "mtime": 1752078877815, "results": "591", "hashOfConfig": "324"}, {"size": 7332, "mtime": 1753436913385, "results": "592", "hashOfConfig": "324"}, {"size": 2476, "mtime": 1752163515003, "results": "593", "hashOfConfig": "324"}, {"size": 16234, "mtime": 1753451951646, "results": "594", "hashOfConfig": "324"}, {"size": 2523, "mtime": 1752652231768, "results": "595", "hashOfConfig": "324"}, {"size": 5217, "mtime": 1752078877820, "results": "596", "hashOfConfig": "324"}, {"size": 7075, "mtime": 1753436913385, "results": "597", "hashOfConfig": "324"}, {"size": 7555, "mtime": 1753436913385, "results": "598", "hashOfConfig": "324"}, {"size": 6825, "mtime": 1752078877824, "results": "599", "hashOfConfig": "324"}, {"size": 7792, "mtime": 1752078877828, "results": "600", "hashOfConfig": "324"}, {"size": 6973, "mtime": 1752078877829, "results": "601", "hashOfConfig": "324"}, {"size": 2892, "mtime": 1752078877829, "results": "602", "hashOfConfig": "324"}, {"size": 6477, "mtime": 1752078877830, "results": "603", "hashOfConfig": "324"}, {"size": 15800, "mtime": 1753436913385, "results": "604", "hashOfConfig": "324"}, {"size": 7803, "mtime": 1752078877832, "results": "605", "hashOfConfig": "324"}, {"size": 2313, "mtime": 1752078877833, "results": "606", "hashOfConfig": "324"}, {"size": 5468, "mtime": 1752078877833, "results": "607", "hashOfConfig": "324"}, {"size": 2609, "mtime": 1752078877834, "results": "608", "hashOfConfig": "324"}, {"size": 4654, "mtime": 1752652313957, "results": "609", "hashOfConfig": "324"}, {"size": 760, "mtime": 1752078877813, "results": "610", "hashOfConfig": "324"}, {"size": 21125, "mtime": 1752078877837, "results": "611", "hashOfConfig": "324"}, {"size": 6062, "mtime": 1752078877838, "results": "612", "hashOfConfig": "324"}, {"size": 17024, "mtime": 1752435156823, "results": "613", "hashOfConfig": "324"}, {"size": 16748, "mtime": 1752078877838, "results": "614", "hashOfConfig": "324"}, {"size": 6083, "mtime": 1753436913393, "results": "615", "hashOfConfig": "324"}, {"size": 4878, "mtime": 1752078877841, "results": "616", "hashOfConfig": "324"}, {"size": 4770, "mtime": 1752078877841, "results": "617", "hashOfConfig": "324"}, {"size": 1410, "mtime": 1752078877841, "results": "618", "hashOfConfig": "324"}, {"size": 791, "mtime": 1752860591670, "results": "619", "hashOfConfig": "324"}, {"size": 5323, "mtime": 1753452346595, "results": "620", "hashOfConfig": "324"}, {"size": 1022, "mtime": 1752078877852, "results": "621", "hashOfConfig": "324"}, {"size": 2650, "mtime": 1752078877852, "results": "622", "hashOfConfig": "324"}, {"size": 26953, "mtime": 1753436913395, "results": "623", "hashOfConfig": "324"}, {"size": 6236, "mtime": 1753436185859, "results": "624", "hashOfConfig": "324"}, {"size": 6726, "mtime": 1752599290194, "results": "625", "hashOfConfig": "324"}, {"size": 4491, "mtime": 1753436913385, "results": "626", "hashOfConfig": "324"}, {"size": 6987, "mtime": 1752675082294, "results": "627", "hashOfConfig": "324"}, {"size": 5954, "mtime": 1752758810126, "results": "628", "hashOfConfig": "324"}, {"size": 2112, "mtime": 1752771048245, "results": "629", "hashOfConfig": "324"}, {"size": 5706, "mtime": 1752926536533, "results": "630", "hashOfConfig": "324"}, {"size": 6994, "mtime": 1752761122820, "results": "631", "hashOfConfig": "324"}, {"size": 1570, "mtime": 1752776247399, "results": "632", "hashOfConfig": "324"}, {"size": 3452, "mtime": 1753436913462, "results": "633", "hashOfConfig": "324"}, {"size": 143, "mtime": 1752750980930, "results": "634", "hashOfConfig": "324"}, {"size": 8146, "mtime": 1753480209077, "results": "635", "hashOfConfig": "324"}, {"size": 5546, "mtime": 1753480596432, "results": "636", "hashOfConfig": "324"}, {"size": 4221, "mtime": 1753480613420, "results": "637", "hashOfConfig": "324"}, {"size": 5271, "mtime": 1753480641552, "results": "638", "hashOfConfig": "324"}, {"size": 6679, "mtime": 1753480657266, "results": "639", "hashOfConfig": "324"}, {"size": 2839, "mtime": 1753456256483, "results": "640", "hashOfConfig": "324"}, {"size": 2830, "mtime": 1753456192051, "results": "641", "hashOfConfig": "324"}, {"size": 4508, "mtime": 1753456319851, "results": "642", "hashOfConfig": "324"}, {"size": 2623, "mtime": 1753479554573, "results": "643", "hashOfConfig": "324"}, {"size": 760, "mtime": 1753479270975, "results": "644", "hashOfConfig": "324"}, {"size": 57483, "mtime": 1753026675695, "results": "645", "hashOfConfig": "324"}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h3uczq", {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1258", "messages": "1259", "suppressedMessages": "1260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1261", "messages": "1262", "suppressedMessages": "1263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1264", "messages": "1265", "suppressedMessages": "1266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1267", "messages": "1268", "suppressedMessages": "1269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1270", "messages": "1271", "suppressedMessages": "1272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1273", "messages": "1274", "suppressedMessages": "1275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1276", "messages": "1277", "suppressedMessages": "1278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1279", "messages": "1280", "suppressedMessages": "1281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1282", "messages": "1283", "suppressedMessages": "1284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1285", "messages": "1286", "suppressedMessages": "1287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1288", "messages": "1289", "suppressedMessages": "1290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1291", "messages": "1292", "suppressedMessages": "1293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1294", "messages": "1295", "suppressedMessages": "1296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1297", "messages": "1298", "suppressedMessages": "1299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1300", "messages": "1301", "suppressedMessages": "1302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1303", "messages": "1304", "suppressedMessages": "1305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1306", "messages": "1307", "suppressedMessages": "1308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1327", "messages": "1328", "suppressedMessages": "1329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1330", "messages": "1331", "suppressedMessages": "1332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1333", "messages": "1334", "suppressedMessages": "1335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1336", "messages": "1337", "suppressedMessages": "1338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1339", "messages": "1340", "suppressedMessages": "1341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1342", "messages": "1343", "suppressedMessages": "1344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1345", "messages": "1346", "suppressedMessages": "1347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1348", "messages": "1349", "suppressedMessages": "1350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1351", "messages": "1352", "suppressedMessages": "1353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1354", "messages": "1355", "suppressedMessages": "1356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1357", "messages": "1358", "suppressedMessages": "1359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1360", "messages": "1361", "suppressedMessages": "1362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1363", "messages": "1364", "suppressedMessages": "1365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1366", "messages": "1367", "suppressedMessages": "1368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1369", "messages": "1370", "suppressedMessages": "1371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1372", "messages": "1373", "suppressedMessages": "1374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1375", "messages": "1376", "suppressedMessages": "1377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1378", "messages": "1379", "suppressedMessages": "1380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1381", "messages": "1382", "suppressedMessages": "1383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1384", "messages": "1385", "suppressedMessages": "1386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1387", "messages": "1388", "suppressedMessages": "1389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1390", "messages": "1391", "suppressedMessages": "1392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1393", "messages": "1394", "suppressedMessages": "1395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1396", "messages": "1397", "suppressedMessages": "1398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1399", "messages": "1400", "suppressedMessages": "1401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1402", "messages": "1403", "suppressedMessages": "1404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1405", "messages": "1406", "suppressedMessages": "1407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1408", "messages": "1409", "suppressedMessages": "1410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1411", "messages": "1412", "suppressedMessages": "1413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1414", "messages": "1415", "suppressedMessages": "1416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1417", "messages": "1418", "suppressedMessages": "1419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1420", "messages": "1421", "suppressedMessages": "1422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1423", "messages": "1424", "suppressedMessages": "1425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1426", "messages": "1427", "suppressedMessages": "1428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1429", "messages": "1430", "suppressedMessages": "1431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1432", "messages": "1433", "suppressedMessages": "1434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1435", "messages": "1436", "suppressedMessages": "1437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1438", "messages": "1439", "suppressedMessages": "1440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1441", "messages": "1442", "suppressedMessages": "1443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1444", "messages": "1445", "suppressedMessages": "1446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1447", "messages": "1448", "suppressedMessages": "1449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1450", "messages": "1451", "suppressedMessages": "1452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1453", "messages": "1454", "suppressedMessages": "1455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1456", "messages": "1457", "suppressedMessages": "1458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1459", "messages": "1460", "suppressedMessages": "1461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1462", "messages": "1463", "suppressedMessages": "1464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1465", "messages": "1466", "suppressedMessages": "1467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1468", "messages": "1469", "suppressedMessages": "1470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1471", "messages": "1472", "suppressedMessages": "1473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1474", "messages": "1475", "suppressedMessages": "1476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1477", "messages": "1478", "suppressedMessages": "1479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1480", "messages": "1481", "suppressedMessages": "1482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1483", "messages": "1484", "suppressedMessages": "1485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1486", "messages": "1487", "suppressedMessages": "1488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1489", "messages": "1490", "suppressedMessages": "1491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1492", "messages": "1493", "suppressedMessages": "1494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1495", "messages": "1496", "suppressedMessages": "1497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1498", "messages": "1499", "suppressedMessages": "1500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1501", "messages": "1502", "suppressedMessages": "1503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1504", "messages": "1505", "suppressedMessages": "1506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1507", "messages": "1508", "suppressedMessages": "1509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1510", "messages": "1511", "suppressedMessages": "1512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1513", "messages": "1514", "suppressedMessages": "1515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1516", "messages": "1517", "suppressedMessages": "1518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1519", "messages": "1520", "suppressedMessages": "1521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1522", "messages": "1523", "suppressedMessages": "1524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1525", "messages": "1526", "suppressedMessages": "1527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1528", "messages": "1529", "suppressedMessages": "1530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1531", "messages": "1532", "suppressedMessages": "1533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1534", "messages": "1535", "suppressedMessages": "1536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1537", "messages": "1538", "suppressedMessages": "1539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1540", "messages": "1541", "suppressedMessages": "1542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1543", "messages": "1544", "suppressedMessages": "1545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1546", "messages": "1547", "suppressedMessages": "1548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1549", "messages": "1550", "suppressedMessages": "1551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1552", "messages": "1553", "suppressedMessages": "1554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1555", "messages": "1556", "suppressedMessages": "1557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1558", "messages": "1559", "suppressedMessages": "1560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1561", "messages": "1562", "suppressedMessages": "1563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1564", "messages": "1565", "suppressedMessages": "1566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1567", "messages": "1568", "suppressedMessages": "1569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1570", "messages": "1571", "suppressedMessages": "1572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1573", "messages": "1574", "suppressedMessages": "1575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1576", "messages": "1577", "suppressedMessages": "1578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1579", "messages": "1580", "suppressedMessages": "1581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1582", "messages": "1583", "suppressedMessages": "1584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1585", "messages": "1586", "suppressedMessages": "1587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1588", "messages": "1589", "suppressedMessages": "1590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1591", "messages": "1592", "suppressedMessages": "1593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1594", "messages": "1595", "suppressedMessages": "1596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1597", "messages": "1598", "suppressedMessages": "1599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1600", "messages": "1601", "suppressedMessages": "1602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1603", "messages": "1604", "suppressedMessages": "1605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1606", "messages": "1607", "suppressedMessages": "1608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1609", "messages": "1610", "suppressedMessages": "1611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\common\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\BusinessCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CategorySelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CompactLocationPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\DiscoverySkeletons.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ErrorComponents.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\FullScreenLocationSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\NavigationHandlers.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ResultsList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SearchSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SortBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\CompactLocationPickerStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\DiscoverScreenStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\LocationSelectorScreenStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\ResultsListStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ViewToggle.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx", [], ["1612", "1613"], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\SafeAreaWrapper.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\StatusBarManager.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessFollowersModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessLikesModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowersList.tsx", [], ["1614", "1615"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesList.tsx", [], ["1616", "1617"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsList.tsx", [], ["1618", "1619"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsSortBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageCardModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageProductsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AdvancedFeaturesSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AppearanceSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BasicInfoSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BusinessDetailsSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\ContactLocationSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\SocialLinksSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\StatusSettingsSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\VariantModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\FollowingList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\LikesList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsSortBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\EditProfileModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\FollowingModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\LikesModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\ReviewsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ColorPickerBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\VariantTypeBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressInformationSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\PersonalInformationSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\FollowingModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\LikesModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ProductsModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ReviewsModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts", [], [], "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts", [], [], "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts", [], [], "C:\\web-app\\dukancard-app\\src\\constants\\predefinedVariants.ts", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\DiscoveryContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\LocationContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useDebounce.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useDynamicSafeArea.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\businessActions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\DiscoveryService.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\locationActions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\productActions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\types.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\locationUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\secureBusinessProfiles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\locationStorageService.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ad.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\auth.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\business\\analytics.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\components.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\discovery.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\profile.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\screens.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ui.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\distanceCalculation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\imageCompression.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\+not-found.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step1AvatarName.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step2AddressLocation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessDeleteAccountModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessStatusSettingsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\GalleryModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ShareBusinessCardModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.styles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\types\\gallery.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessReviewsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowingList.tsx", [], ["1620", "1621"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesGivenList.tsx", [], ["1622", "1623"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsGivenList.tsx", [], ["1624", "1625"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsList.tsx", [], ["1626"], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessFollowersModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessLikesModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessReviewsModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\config\\supabase\\constants.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\business.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts", [], [], {"ruleId": "1627", "severity": 1, "message": "1628", "line": 389, "column": 6, "nodeType": "1629", "endLine": 389, "endColumn": 56, "suggestions": "1630", "suppressions": "1631"}, {"ruleId": "1627", "severity": 1, "message": "1632", "line": 409, "column": 6, "nodeType": "1629", "endLine": 409, "endColumn": 69, "suggestions": "1633", "suppressions": "1634"}, {"ruleId": "1627", "severity": 1, "message": "1635", "line": 88, "column": 6, "nodeType": "1629", "endLine": 88, "endColumn": 30, "suggestions": "1636", "suppressions": "1637"}, {"ruleId": "1627", "severity": 1, "message": "1635", "line": 95, "column": 6, "nodeType": "1629", "endLine": 95, "endColumn": 12, "suggestions": "1638", "suppressions": "1639"}, {"ruleId": "1627", "severity": 1, "message": "1640", "line": 105, "column": 6, "nodeType": "1629", "endLine": 105, "endColumn": 30, "suggestions": "1641", "suppressions": "1642"}, {"ruleId": "1627", "severity": 1, "message": "1640", "line": 112, "column": 6, "nodeType": "1629", "endLine": 112, "endColumn": 12, "suggestions": "1643", "suppressions": "1644"}, {"ruleId": "1627", "severity": 1, "message": "1645", "line": 237, "column": 6, "nodeType": "1629", "endLine": 237, "endColumn": 12, "suggestions": "1646", "suppressions": "1647"}, {"ruleId": "1627", "severity": 1, "message": "1648", "line": 248, "column": 6, "nodeType": "1629", "endLine": 248, "endColumn": 32, "suggestions": "1649", "suppressions": "1650"}, {"ruleId": "1627", "severity": 1, "message": "1651", "line": 92, "column": 6, "nodeType": "1629", "endLine": 92, "endColumn": 24, "suggestions": "1652", "suppressions": "1653"}, {"ruleId": "1627", "severity": 1, "message": "1651", "line": 99, "column": 6, "nodeType": "1629", "endLine": 99, "endColumn": 12, "suggestions": "1654", "suppressions": "1655"}, {"ruleId": "1627", "severity": 1, "message": "1640", "line": 86, "column": 6, "nodeType": "1629", "endLine": 86, "endColumn": 24, "suggestions": "1656", "suppressions": "1657"}, {"ruleId": "1627", "severity": 1, "message": "1640", "line": 93, "column": 6, "nodeType": "1629", "endLine": 93, "endColumn": 12, "suggestions": "1658", "suppressions": "1659"}, {"ruleId": "1627", "severity": 1, "message": "1660", "line": 95, "column": 6, "nodeType": "1629", "endLine": 95, "endColumn": 32, "suggestions": "1661", "suppressions": "1662"}, {"ruleId": "1627", "severity": 1, "message": "1660", "line": 102, "column": 6, "nodeType": "1629", "endLine": 102, "endColumn": 12, "suggestions": "1663", "suppressions": "1664"}, {"ruleId": "1627", "severity": 1, "message": "1660", "line": 172, "column": 6, "nodeType": "1629", "endLine": 172, "endColumn": 38, "suggestions": "1665", "suppressions": "1666"}, "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'styles.loadingFooter' and 'styles.loadingText'. Either include them or remove the dependency array.", "ArrayExpression", ["1667"], ["1668"], "React Hook useCallback has missing dependencies: 'isLoading', 'styles.emptyContainer', 'styles.emptySubtitle', and 'styles.emptyTitle'. Either include them or remove the dependency array.", ["1669"], ["1670"], "React Hook useEffect has a missing dependency: 'fetchFollowers'. Either include it or remove the dependency array.", ["1671"], ["1672"], ["1673"], ["1674"], "React Hook useEffect has a missing dependency: 'fetchLikes'. Either include it or remove the dependency array.", ["1675"], ["1676"], ["1677"], ["1678"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["1679"], ["1680"], "React Hook useEffect has missing dependencies: 'fetchProducts' and 'user'. Either include them or remove the dependency array.", ["1681"], ["1682"], "React Hook useEffect has a missing dependency: 'fetchSubscriptions'. Either include it or remove the dependency array.", ["1683"], ["1684"], ["1685"], ["1686"], ["1687"], ["1688"], ["1689"], ["1690"], "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["1691"], ["1692"], ["1693"], ["1694"], ["1695"], ["1696"], {"desc": "1697", "fix": "1698"}, {"kind": "1699", "justification": "1700"}, {"desc": "1701", "fix": "1702"}, {"kind": "1699", "justification": "1700"}, {"desc": "1703", "fix": "1704"}, {"kind": "1699", "justification": "1700"}, {"desc": "1705", "fix": "1706"}, {"kind": "1699", "justification": "1700"}, {"desc": "1707", "fix": "1708"}, {"kind": "1699", "justification": "1700"}, {"desc": "1709", "fix": "1710"}, {"kind": "1699", "justification": "1700"}, {"desc": "1711", "fix": "1712"}, {"kind": "1699", "justification": "1700"}, {"desc": "1713", "fix": "1714"}, {"kind": "1699", "justification": "1700"}, {"desc": "1715", "fix": "1716"}, {"kind": "1699", "justification": "1700"}, {"desc": "1717", "fix": "1718"}, {"kind": "1699", "justification": "1700"}, {"desc": "1719", "fix": "1720"}, {"kind": "1699", "justification": "1700"}, {"desc": "1709", "fix": "1721"}, {"kind": "1699", "justification": "1700"}, {"desc": "1722", "fix": "1723"}, {"kind": "1699", "justification": "1700"}, {"desc": "1724", "fix": "1725"}, {"kind": "1699", "justification": "1700"}, {"desc": "1726", "fix": "1727"}, {"kind": "1699", "justification": "1700"}, "Update the dependencies array to be: [isLoading, hasMore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", {"range": "1728", "text": "1729"}, "directive", "", "Update the dependencies array to be: [isLoading, posts.length, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]", {"range": "1730", "text": "1731"}, "Update the dependencies array to be: [businessId, fetchFollowers, searchTerm]", {"range": "1732", "text": "1733"}, "Update the dependencies array to be: [fetchFollowers, page]", {"range": "1734", "text": "1735"}, "Update the dependencies array to be: [businessId, fetchLikes, searchTerm]", {"range": "1736", "text": "1737"}, "Update the dependencies array to be: [fetchLikes, page]", {"range": "1738", "text": "1739"}, "Update the dependencies array to be: [fetchProducts, user]", {"range": "1740", "text": "1741"}, "Update the dependencies array to be: [activeSearchTerm, fetchProducts, sortBy, user]", {"range": "1742", "text": "1743"}, "Update the dependencies array to be: [user, searchTerm, fetchSubscriptions]", {"range": "1744", "text": "1745"}, "Update the dependencies array to be: [fetchSubscriptions, page]", {"range": "1746", "text": "1747"}, "Update the dependencies array to be: [user, searchTerm, fetchLikes]", {"range": "1748", "text": "1749"}, {"range": "1750", "text": "1739"}, "Update the dependencies array to be: [user, sortBy, searchTerm, fetchReviews]", {"range": "1751", "text": "1752"}, "Update the dependencies array to be: [fetchReviews, page]", {"range": "1753", "text": "1754"}, "Update the dependencies array to be: [businessId, sortBy, searchTerm, fetchReviews]", {"range": "1755", "text": "1756"}, [11773, 11823], "[isLoading, has<PERSON>ore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", [12427, 12490], "[isLoading, posts.length, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]", [2771, 2795], "[businessId, fetchFollowers, searchTerm]", [2941, 2947], "[fetch<PERSON><PERSON><PERSON><PERSON>, page]", [2821, 2845], "[businessId, fetchLikes, searchTerm]", [2980, 2986], "[fetchLikes, page]", [7511, 7517], "[fetchProducts, user]", [7809, 7835], "[activeSearchTerm, fetchProducts, sortBy, user]", [2901, 2919], "[user, searchTerm, fetchSubscriptions]", [3062, 3068], "[fetchSubscriptions, page]", [2453, 2471], "[user, searchTerm, fetchLikes]", [2606, 2612], [2773, 2799], "[user, sortBy, searchTerm, fetchReviews]", [2936, 2942], "[fetchR<PERSON>ie<PERSON>, page]", [5098, 5130], "[businessId, sortBy, searchTerm, fetchReviews]"]