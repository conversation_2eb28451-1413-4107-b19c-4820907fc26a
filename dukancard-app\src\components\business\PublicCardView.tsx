import React, { useState, useCallback } from "react";
import {
  View,
  ScrollView,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Linking,
  Alert,
} from "react-native";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { BusinessDiscoveryData, BusinessGalleryImage } from "@/src/types/business";

import {
  calculateDistanceFromCurrentLocation,
  formatDistance,
} from "@/src/utils/distanceCalculation";
import { useLocation } from "@/src/contexts/LocationContext";

import { PublicCardTabSkeleton } from "../ui/SkeletonLoader";
import { createPublicCardViewStyles } from "@/styles/PublicCardViewStyles";
import ReviewModal from "./ReviewModal";
import FullScreenImageViewer from "./FullScreenImageViewer";
import { AdData, BusinessCustomAd } from "@/src/types/ad";
import EnhancedAdSection from "../ads/EnhancedAdSection";

// Import new components
import PublicCardHeader from "./PublicCardHeader";
import BusinessStats from "./BusinessStats";

import TabNavigation, { TabType } from "./TabNavigation";
import AboutTab from "./AboutTab";
import ProductsTab from "./ProductsTab";
import GalleryTab from "./GalleryTab";
import ReviewsTab from "./ReviewsTab";

// Import custom hooks
import {
  useBusinessCardData,
  useProductsPagination,
  useReviewsPagination,
} from "@/src/hooks/useBusinessCardData";
import { useBusinessInteractions } from "@/src/hooks/useBusinessInteractions";

interface PublicCardViewProps {
  businessData: BusinessDiscoveryData;
  onClose?: () => void;
}

export default function PublicCardView({
  businessData,
  onClose,
}: PublicCardViewProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";
  const styles = createPublicCardViewStyles(isDark);

  const [selectedTab, setSelectedTab] = useState<TabType>("products");
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [fullScreenImageVisible, setFullScreenImageVisible] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  // Custom hooks
  const { currentLocation } = useLocation();
  const { cardData, loadingCardData, adData, adLoading } = useBusinessCardData(
    businessData.data?.id || "",
    businessData.data?.business_slug || "",
    businessData.data?.pincode || undefined
  );

  // Calculate distance using current location from LocationContext
  const getDistanceText = () => {
    if (!businessData.data?.latitude || !businessData.data?.longitude) {
      return null;
    }

    const distance = calculateDistanceFromCurrentLocation(
      currentLocation,
      businessData.data.latitude,
      businessData.data.longitude
    );

    if (distance !== null) {
      return formatDistance(distance);
    }

    return null;
  };

  const distanceText = getDistanceText();
  const { interactionStatus,
    isOwner,
    likeLoading,
    subscribeLoading,
    handleLikePress,
    handleSubscribePress,
    handleReviewPress,
    handleReviewSubmitted,
  } = useBusinessInteractions(businessData.data?.id || "");

  const {
    allProducts,
    loadingMoreProducts,
    hasMoreProducts,
    searchQuery,
    sortBy,
    loadMoreProducts,
    searchProducts,
    sortProducts,
  } = useProductsPagination(businessData.data?.id || '', cardData?.products || []);

  const {
    allReviews,
    loadingMoreReviews,
    hasMoreReviews,
    sortBy: reviewSortBy,
    loadMoreReviews,
    sortReviews,
  } = useReviewsPagination(businessData.data?.id || '', cardData?.reviews || []);

  // Handle gallery image press
  const handleGalleryImagePress = (index: number) => {
    setSelectedImageIndex(index);
    setFullScreenImageVisible(true);
  };

  const handleDirectionsPress = () => {
    if (!businessData.data?.latitude || !businessData.data?.longitude) {
      Alert.alert("Error", "Business location not available");
      return;
    }

    const url = `https://www.google.com/maps/dir/?api=1&destination=${businessData.data.latitude},${businessData.data.longitude}`;

    Alert.alert(
      "Open Navigation",
      "This will open directions in your default maps app.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Open",
          onPress: () => {
            Linking.openURL(url).catch((err) => {
              console.error("Failed to open navigation:", err);
              Alert.alert("Error", "Unable to open navigation app");
            });
          },
        },
      ]
    );
  };

  // Handle scroll for infinite loading
  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { layoutMeasurement, contentOffset, contentSize } =
        event.nativeEvent;
      const paddingToBottom = 200; // Trigger earlier for better UX
      const isCloseToBottom =
        layoutMeasurement.height + contentOffset.y >=
        contentSize.height - paddingToBottom;

      if (isCloseToBottom) {
        // Check which tab is active and trigger appropriate load more
        if (
          selectedTab === "products" &&
          hasMoreProducts &&
          !loadingMoreProducts
        ) {
          loadMoreProducts();
        } else if (
          selectedTab === "reviews" &&
          hasMoreReviews &&
          !loadingMoreReviews
        ) {
          loadMoreReviews();
        }
      }
    },
    [
      selectedTab,
      hasMoreProducts,
      loadingMoreProducts,
      hasMoreReviews,
      loadingMoreReviews,
      loadMoreProducts,
      loadMoreReviews,
    ]
  );

  const renderTabContent = () => {
    if (loadingCardData) {
      return <PublicCardTabSkeleton tab={selectedTab} />;
    }

    switch (selectedTab) {
      case "about":
        return <AboutTab businessData={businessData} isDark={isDark} />;
      case "products":
        return (
          <ProductsTab
            products={allProducts}
            businessId={businessData.data?.id || ""}
            isDark={isDark}
            loadingMore={loadingMoreProducts}
            hasMore={hasMoreProducts}
            searchQuery={searchQuery}
            sortBy={sortBy}
            onLoadMore={loadMoreProducts}
            onSearch={searchProducts}
            onSort={sortProducts}
            useScrollView={true} // Use ScrollView instead of FlatList
            searchSortLoading={loadingMoreProducts && allProducts.length === 0} // Show skeleton when loading and no products
          />
        );
      case "gallery":
        return (
          <GalleryTab
            gallery={cardData?.gallery || []}
            isDark={isDark}
            onImagePress={handleGalleryImagePress}
          />
        );
      case "reviews":
        return (
          <ReviewsTab
            reviews={allReviews}
            reviewStats={cardData?.reviewStats || null}
            isDark={isDark}
            loadingMore={loadingMoreReviews}
            hasMore={hasMoreReviews}
            sortBy={reviewSortBy}
            onLoadMore={loadMoreReviews}
            onSort={sortReviews}
            useScrollView={true} // Use ScrollView instead of FlatList
            sortLoading={loadingMoreReviews && allReviews.length === 0} // Show skeleton when loading and no reviews
          />
        );
      default:
        return (
          <ProductsTab
            products={allProducts}
            businessId={businessData.data?.id || ""}
            isDark={isDark}
            loadingMore={loadingMoreProducts}
            hasMore={hasMoreProducts}
            searchQuery={searchQuery}
            sortBy={sortBy}
            onLoadMore={loadMoreProducts}
            onSearch={searchProducts}
            onSort={sortProducts}
            useScrollView={true} // Use ScrollView instead of FlatList
            searchSortLoading={loadingMoreProducts && allProducts.length === 0} // Show skeleton when loading and no products
          />
        );
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={200}
      >
        {/* Header Section */}
        <PublicCardHeader
          businessData={businessData}
          isDark={isDark}
          onClose={onClose}
          distanceKm={distanceText}
        />

        {/* Enhanced Stats Section with Interactive Buttons */}
        <BusinessStats
          businessData={businessData}
          interactionStatus={interactionStatus}
          isDark={isDark}
          isOwner={isOwner}
          likeLoading={likeLoading}
          subscribeLoading={subscribeLoading}
          onLikePress={handleLikePress}
          onSubscribePress={handleSubscribePress}
          onReviewPress={() =>
            handleReviewPress(() => setReviewModalVisible(true))
          }
          onDirectionsPress={handleDirectionsPress}
        />

        {/* Enhanced Ad Section */}
        <EnhancedAdSection
          topAdData={adData}
          businessCustomAd={businessData.data?.[COLUMNS.CUSTOM_ADS] as BusinessCustomAd}
          userPlan={businessData.data?.[COLUMNS.USER_PLAN] || undefined}
          loading={adLoading} // Show skeleton while ads are being fetched
        />

        {/* Tab Navigation */}
        <TabNavigation
          selectedTab={selectedTab}
          onTabChange={setSelectedTab}
          isDark={isDark}
        />

        {/* Tab Content */}
        {renderTabContent()}
      </ScrollView>

      {/* Review Modal */}
      <ReviewModal
        visible={reviewModalVisible}
        onClose={() => setReviewModalVisible(false)}
        businessId={businessData.data?.id || ""}
        businessName={businessData.data?.business_name || ""}
        existingRating={interactionStatus?.userRating}
        existingReview={interactionStatus?.userReview}
        onReviewSubmitted={handleReviewSubmitted}
      />

      {/* Full Screen Image Viewer */}
      <FullScreenImageViewer
        visible={fullScreenImageVisible}
        images={(businessData.data?.gallery as BusinessGalleryImage[])?.map((img) => ({
          id: img.id || '',
          url: img.url,
          caption: img.caption
        })) || []}
        initialIndex={selectedImageIndex}
        onClose={() => setFullScreenImageVisible(false)}
      />
    </View>
  );
}
