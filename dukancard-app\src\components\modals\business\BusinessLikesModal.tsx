import React, { useState } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from "react-native";
import { X, Search } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createBusinessLikesModalStyles } from "@/styles/modals/business/business-likes-modal";
import BusinessLikesList from "./components/BusinessLikesList";
import LikesList from "../customer/components/LikesList";

interface BusinessLikesModalProps {
  visible: boolean;
  onClose: () => void;
  businessId: string;
}

export default function BusinessLikesModal({
  visible,
  onClose,
  businessId,
}: BusinessLikesModalProps) {
  const theme = useTheme();
  const styles = createBusinessLikesModalStyles(theme);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeSearchTerm, setActiveSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"received" | "given">("received");

  const handleSearch = () => {
    setActiveSearchTerm(searchTerm);
  };

  const handleSearchSubmit = () => {
    handleSearch();
  };

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      animationType="slide"
    >
      <View style={styles.modalContainer}>
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
          >
            <View style={styles.header}>
              <View style={{ width: 40 }} />
              <Text style={styles.headerTitle}>Likes</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <X size={24} color={theme.colors.foreground} />
              </TouchableOpacity>
            </View>

            {/* Toggle Section */}
            <View style={styles.toggleContainer}>
              <TouchableOpacity
                style={[
                  styles.toggleButton,
                  activeTab === "received" && styles.toggleButtonActive,
                ]}
                onPress={() => setActiveTab("received")}
              >
                <Text
                  style={[
                    styles.toggleButtonText,
                    activeTab === "received" && styles.toggleButtonTextActive,
                  ]}
                >
                  Received
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.toggleButton,
                  activeTab === "given" && styles.toggleButtonActive,
                ]}
                onPress={() => setActiveTab("given")}
              >
                <Text
                  style={[
                    styles.toggleButtonText,
                    activeTab === "given" && styles.toggleButtonTextActive,
                  ]}
                >
                  Given
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.contentContainer}>
              <View style={styles.searchContainer}>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    backgroundColor: theme.isDark
                      ? theme.colors.input
                      : theme.colors.card,
                    borderRadius: theme.borderRadius.md,
                    paddingHorizontal: theme.spacing.md,
                    borderWidth: 1,
                    borderColor: theme.colors.border,
                  }}
                >
                  <Search
                    size={20}
                    color={theme.colors.textSecondary}
                    style={{ marginRight: theme.spacing.sm }}
                  />
                  <TextInput
                    style={{
                      flex: 1,
                      height: 48,
                      color: theme.colors.foreground,
                      fontSize: theme.typography.fontSize.base,
                    }}
                    placeholder="Search by name..."
                    placeholderTextColor={theme.colors.textSecondary}
                    value={searchTerm}
                    onChangeText={setSearchTerm}
                    onSubmitEditing={handleSearchSubmit}
                    returnKeyType="search"
                  />
                  {searchTerm.length > 0 && (
                    <TouchableOpacity
                      onPress={handleSearch}
                      style={{
                        padding: theme.spacing.xs,
                        marginLeft: theme.spacing.xs,
                      }}
                    >
                      <Search size={20} color={theme.colors.primary} />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              {activeTab === "received" ? (
                <BusinessLikesList
                  businessId={businessId}
                  searchTerm={activeSearchTerm}
                />
              ) : (
                <LikesList searchTerm={activeSearchTerm} />
              )}
            </View>
          </KeyboardAvoidingView>
      </SafeAreaView>
      </View>
    </Modal>
  );
}
