import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from "react-native";
import { X, ChevronDown, Search } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createBusinessReviewsModalStyles } from "@/styles/modals/business/business-reviews-modal";
import BusinessReviewsGivenList from "./components/BusinessReviewsGivenList";
import BusinessReviewsList from "./components/BusinessReviewsList";
import ReviewsSortBottomSheet, {
  ReviewsSortBottomSheetRef,
  ReviewSortOption
} from "../customer/components/ReviewsSortBottomSheet";
import { useAuth } from "@/src/contexts/AuthContext";
import { reviewsService } from "@/backend/supabase/services/posts/socialService";
import { businessSocialService } from "@/backend/supabase/services/business/businessSocialService";
import { formatIndianNumberShort } from "@/lib/utils";

interface BusinessReviewsModalProps {
  visible: boolean;
  onClose: () => void;
  businessId: string;
}

export default function BusinessReviewsModal({
  visible,
  onClose,
  businessId
}: BusinessReviewsModalProps) {
  const theme = useTheme();
  const styles = createBusinessReviewsModalStyles(theme);
  const { user } = useAuth();
  const sortBottomSheetRef = useRef<ReviewsSortBottomSheetRef>(null);

  const [sortBy, setSortBy] = useState<ReviewSortOption>("newest");
  const [reviewCount, setReviewCount] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeSearchTerm, setActiveSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"received" | "given">("received");

  const handleSearch = () => {
    setActiveSearchTerm(searchTerm);
  };

  const handleSearchSubmit = () => {
    handleSearch();
  };

  // Fetch review count based on active tab
  const fetchReviewCount = useCallback(async () => {
    if (!user?.id) return;

    try {
      if (activeTab === "received") {
        // Fetch reviews received by this business
        const result = await businessSocialService.fetchBusinessReviewsReceived(businessId, 1, 1);
        setReviewCount(result.totalCount);
      } else {
        // Fetch reviews given by this business
        const result = await reviewsService.fetchReviews(user.id, 1, 1, sortBy);
        setReviewCount(result.totalCount);
      }
    } catch (error) {
      console.error("Failed to fetch review count:", error);
    }
  }, [user?.id, businessId, activeTab, sortBy]);

  useEffect(() => {
    if (visible) {
      fetchReviewCount();
    }
  }, [visible, fetchReviewCount]);

  const handleSortPress = () => {
    sortBottomSheetRef.current?.present();
  };

  const handleSortSelect = (newSortBy: ReviewSortOption) => {
    setSortBy(newSortBy);
  };

  const getSortDisplayName = (sort: ReviewSortOption): string => {
    switch (sort) {
      case "newest":
        return "Newest First";
      case "oldest":
        return "Oldest First";
      case "rating_high":
        return "Highest Rating";
      case "rating_low":
        return "Lowest Rating";
      default:
        return "Newest First";
    }
  };

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      animationType="slide"
    >
      <View style={styles.modalContainer}>
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
          >
            <View style={styles.header}>
              <View style={{ width: 40 }} />
              <View style={{ alignItems: 'center' }}>
                <Text style={styles.headerTitle}>Reviews</Text>
                {reviewCount > 0 && (
                  <Text style={styles.headerSubtitle}>
                    {formatIndianNumberShort(reviewCount)} {reviewCount === 1 ? 'review' : 'reviews'}
                  </Text>
                )}
              </View>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <X size={24} color={theme.colors.foreground} />
              </TouchableOpacity>
            </View>

            {/* Toggle Section */}
            <View style={styles.toggleContainer}>
              <TouchableOpacity
                style={[
                  styles.toggleButton,
                  activeTab === "received" && styles.toggleButtonActive,
                ]}
                onPress={() => setActiveTab("received")}
              >
                <Text
                  style={[
                    styles.toggleButtonText,
                    activeTab === "received" && styles.toggleButtonTextActive,
                  ]}
                >
                  Received
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.toggleButton,
                  activeTab === "given" && styles.toggleButtonActive,
                ]}
                onPress={() => setActiveTab("given")}
              >
                <Text
                  style={[
                    styles.toggleButtonText,
                    activeTab === "given" && styles.toggleButtonTextActive,
                  ]}
                >
                  Given
                </Text>
              </TouchableOpacity>
            </View>

            {/* Review Count and Sort Section */}
            <View style={styles.reviewsHeaderSection}>
              <Text style={styles.reviewCountText}>
                {formatIndianNumberShort(reviewCount)} {reviewCount === 1 ? 'review' : 'reviews'}
              </Text>
              <TouchableOpacity style={styles.sortButton} onPress={handleSortPress}>
                <Text style={styles.sortButtonText}>
                  {getSortDisplayName(sortBy)}
                </Text>
                <ChevronDown size={16} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>

            {/* Search Section */}
            <View style={styles.searchContainer}>
              <View style={styles.searchInputContainer}>
                <Search
                  size={20}
                  color={theme.colors.textSecondary}
                  style={{ marginRight: theme.spacing.sm }}
                />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search by business name..."
                  placeholderTextColor={theme.colors.textSecondary}
                  value={searchTerm}
                  onChangeText={setSearchTerm}
                  onSubmitEditing={handleSearchSubmit}
                  returnKeyType="search"
                />
                {searchTerm.length > 0 && (
                  <TouchableOpacity
                    onPress={handleSearch}
                    style={{
                      padding: theme.spacing.xs,
                      marginLeft: theme.spacing.xs,
                    }}
                  >
                    <Search size={20} color={theme.colors.primary} />
                  </TouchableOpacity>
                )}
              </View>
            </View>

            <View style={styles.contentContainer}>

              {activeTab === "received" ? (
                <BusinessReviewsList
                  businessId={businessId}
                  sortBy={sortBy}
                  searchTerm={activeSearchTerm}
                  onReviewCountChange={setReviewCount}
                />
              ) : (
                <BusinessReviewsGivenList
                  sortBy={sortBy}
                  searchTerm={activeSearchTerm}
                  onReviewCountChange={setReviewCount}
                />
              )}
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </View>

      {/* Sort Bottom Sheet */}
      <ReviewsSortBottomSheet
        ref={sortBottomSheetRef}
        sortBy={sortBy}
        onSortSelect={handleSortSelect}
      />
    </Modal>
  );
}
