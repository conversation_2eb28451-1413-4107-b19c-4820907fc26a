import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  RefreshControl,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import {
  fetchBusinessFollowers,
  FollowerWithProfile,
} from "@/backend/supabase/services/business/businessSocialService";
import { createBusinessFollowersModalStyles } from "@/styles/modals/business/business-followers-modal";
import { ErrorState } from "@/src/components/ui/ErrorState";
import { BusinessFollowersModalSkeleton } from "@/src/components/skeletons/modals/BusinessFollowersModalSkeleton";
import { logError, handleNetworkError } from "@/src/utils/errorHandling";

interface BusinessFollowersListProps {
  businessId: string;
  searchTerm: string;
}

export default function BusinessFollowersList({
  businessId,
  searchTerm,
}: BusinessFollowersListProps) {
  const theme = useTheme();
  const styles = createBusinessFollowersModalStyles(theme);
  const [followers, setFollowers] = useState<FollowerWithProfile[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<any>(null);

  const fetchFollowers = useCallback(
    async (isRefreshing = false) => {
      if (!businessId) return;
      if ((loading && !isRefreshing) || (loadingMore && !isRefreshing)) return;

      if (isRefreshing) {
        setRefreshing(true);
        setPage(1);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      setError(null);

      try {
        const currentPage = isRefreshing ? 1 : page;
        const result = await fetchBusinessFollowers(
          businessId,
          currentPage,
          10
        );

        if (isRefreshing || currentPage === 1) {
          setFollowers(result.items);
        } else {
          setFollowers((prev) => [...prev, ...result.items]);
        }
        setHasMore(result.hasMore);
      } catch (err) {
        const appError = handleNetworkError(err);
        setError(appError);
        logError(appError, "BusinessFollowersList.fetchFollowers");
      } finally {
        setLoading(false);
        setLoadingMore(false);
        setRefreshing(false);
      }
    },
    [businessId, page, loading, loadingMore]
  );

  useEffect(() => {
    setPage(1);
    setFollowers([]);
    setHasMore(true);
    fetchFollowers(true);
  }, [businessId, searchTerm]);

  useEffect(() => {
    if (page > 1) {
      fetchFollowers();
    }
  }, [page]);

  const onRefresh = () => {
    setPage(1);
    fetchFollowers(true);
  };

  const renderItem = ({ item }: { item: FollowerWithProfile }) => {
    const profile = item.profile;
    const name = profile?.name;
    const avatarUrl =
      profile?.type === "business" ? profile?.logo_url : profile?.avatar_url;

    return (
      <TouchableOpacity style={styles.followerItem}>
        <Image
          source={{
            uri:
              avatarUrl ||
              "https://asset.brandfetch.io/id235U50sE/idj9kF8hYy.jpeg",
          }}
          style={styles.avatar}
        />
        <View style={styles.followerContent}>
          <Text style={styles.followerName} numberOfLines={1}>
            {name || "Anonymous"}
          </Text>
          <Text style={styles.followerType} numberOfLines={1}>
            {profile?.type === "business" ? "Business" : "Customer"}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  };

  const handleRefresh = () => {
    fetchFollowers(true);
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore && !loading) {
      setPage((prevPage) => prevPage + 1);
    }
  };

  // Show skeleton on initial load
  if (loading && page === 1) {
    return <BusinessFollowersModalSkeleton />;
  }

  // Show error state
  if (error && followers.length === 0) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error.message || 'Failed to load followers'}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show empty state
  if (!loading && followers.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>No Followers Yet</Text>
        <Text style={styles.emptyText}>
          When customers and businesses follow your business, they'll appear here.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.listContainer}>
      <FlatList
        data={followers}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContentContainer}
      />
    </View>
  );
}
