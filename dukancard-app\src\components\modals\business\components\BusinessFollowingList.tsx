import React, { useState, useCallback, useEffect } from "react";
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from "react-native";
import { useAuth } from "@/src/contexts/AuthContext";
import {
  subscriptionsService,
  SubscriptionWithProfile,
} from "@/backend/supabase/services/posts/socialService";
import { SubscriptionCard } from "@/src/components/social/SubscriptionCard";
import { BusinessFollowersModalSkeleton } from "@/src/components/skeletons/modals/BusinessFollowersModalSkeleton";
import { ErrorState } from "@/src/components/ui/ErrorState";
import { useTheme } from "@/src/hooks/useTheme";
import { createBusinessFollowersModalStyles } from "@/styles/modals/business/business-followers-modal";

interface BusinessFollowingListProps {
  searchTerm: string;
}

export function BusinessFollowingList({ searchTerm }: BusinessFollowingListProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const styles = createBusinessFollowersModalStyles(theme);
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [error, setError] = useState<{ title: string; message: string } | null>(null);

  const fetchSubscriptions = useCallback(
    async (isRefresh = false) => {
      if (!user) return;

      const currentPage = isRefresh ? 1 : page;
      
      if (currentPage === 1 && !isRefresh) {
        setIsLoading(true);
      } else if (currentPage > 1) {
        setLoadingMore(true);
      }
      if (isRefresh) {
        setIsRefreshing(true);
      }

      try {
        const { items, hasMore: newHasMore } = await subscriptionsService.fetchSubscriptions(
          user.id,
          currentPage,
          10,
          searchTerm
        );

        if (isRefresh || currentPage === 1) {
          setSubscriptions(items);
          setPage(2);
        } else {
          setSubscriptions((prev) => [...prev, ...items]);
          setPage(currentPage + 1);
        }
        setHasMore(newHasMore);
        setError(null);
      } catch (err) {
        console.error("Error fetching subscriptions:", err);
        setError({
          title: "Failed to Load Following",
          message: "Unable to load your following list. Please try again.",
        });
      } finally {
        setIsLoading(false);
        setLoadingMore(false);
        if (isRefresh) {
          setIsRefreshing(false);
        }
      }
    },
    [user, page, searchTerm]
  );

  useEffect(() => {
    setPage(1);
    setSubscriptions([]);
    setHasMore(true);
    fetchSubscriptions(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, searchTerm]);

  useEffect(() => {
    if (page > 1) {
      fetchSubscriptions();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page]);

  const handleRefresh = () => {
    setPage(1);
    fetchSubscriptions(true);
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore) {
      fetchSubscriptions();
    }
  };

  const handleUnsubscribe = async (subscriptionId: string) => {
    Alert.alert(
      "Unfollow Business",
      "Are you sure you want to unfollow this business?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Unfollow",
          style: "destructive",
          onPress: async () => {
            try {
              await subscriptionsService.unsubscribe(subscriptionId);
              setSubscriptions((prev) =>
                prev.filter((sub) => sub.id !== subscriptionId)
              );
            } catch (error) {
              console.error("Failed to unsubscribe:", error);
              Alert.alert("Error", "Failed to unfollow business. Please try again.");
            }
          },
        },
      ]
    );
  };

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoadingContainer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };

  if (isLoading) {
    return <BusinessFollowersModalSkeleton />;
  }

  if (error && subscriptions.length === 0) {
    return (
      <ErrorState
        title={error.title}
        message={error.message}
        onRetry={handleRefresh}
      />
    );
  }

  return (
    <View style={styles.listContainer}>
      <FlatList
        data={subscriptions}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <SubscriptionCard
            subscription={item}
            onUnsubscribe={handleUnsubscribe}
          />
        )}
        contentContainerStyle={styles.listContentContainer}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl 
            refreshing={isRefreshing} 
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          !isLoading ? (
            <ErrorState
              title="No Followings Found"
              message="You are not following any businesses yet."
            />
          ) : null
        }
      />
    </View>
  );
}
