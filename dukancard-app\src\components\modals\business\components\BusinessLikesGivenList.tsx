import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import {
  likesService,
  LikeWithProfile,
} from "@/backend/supabase/services/posts/socialService";
import { useAuth } from "@/src/contexts/AuthContext";
import { LikeCard } from "@/src/components/social/LikeCard";
import { BusinessLikesModalSkeleton } from "@/src/components/skeletons/modals/BusinessLikesModalSkeleton";
import { createBusinessLikesModalStyles } from "@/styles/modals/business/business-likes-modal";
import { useTheme } from "@/src/hooks/useTheme";

interface BusinessLikesGivenListProps {
  searchTerm: string;
}

export default function BusinessLikesGivenList({
  searchTerm,
}: BusinessLikesGivenListProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const styles = createBusinessLikesModalStyles(theme);
  const [likes, setLikes] = useState<LikeWithProfile[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchLikes = useCallback(
    async (isRefreshing = false) => {
      if (!user) return;

      const currentPage = isRefreshing ? 1 : page;

      if (currentPage === 1 && !isRefreshing) {
        setLoading(true);
      } else if (currentPage > 1) {
        setLoadingMore(true);
      }
      if (isRefreshing) {
        setRefreshing(true);
      }

      try {
        const { items, hasMore: newHasMore } = await likesService.fetchLikes(
          user.id,
          currentPage,
          10,
          searchTerm
        );

        if (isRefreshing || currentPage === 1) {
          setLikes(items);
          setPage(2);
        } else {
          setLikes((prevLikes) => [...prevLikes, ...items]);
          setPage(currentPage + 1);
        }
        setHasMore(newHasMore);
      } catch (error) {
        console.error("Failed to fetch likes:", error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
        if (isRefreshing) {
          setRefreshing(false);
        }
      }
    },
    [user, page, searchTerm]
  );

  useEffect(() => {
    setPage(1);
    setLikes([]);
    setHasMore(true);
    fetchLikes(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, searchTerm]);

  useEffect(() => {
    if (page > 1) {
      fetchLikes();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page]);

  const handleRefresh = () => {
    setPage(1);
    fetchLikes(true);
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore) {
      fetchLikes();
    }
  };

  const handleUnlike = async (likeId: string) => {
    try {
      await likesService.unlike(likeId);
      setLikes((prevLikes) => prevLikes.filter((like) => like.id !== likeId));
    } catch (error) {
      console.error("Failed to unlike:", error);
    }
  };

  if (loading) {
    return <BusinessLikesModalSkeleton />;
  }

  return (
    <View style={styles.listContainer}>
      <FlatList
        data={likes}
        renderItem={({ item }) => (
          <LikeCard like={item} onUnlike={handleUnlike} />
        )}
        keyExtractor={(item) => item.id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={
          loadingMore ? (
            <View style={styles.footerLoadingContainer}>
              <ActivityIndicator size="small" color={theme.colors.primary} />
            </View>
          ) : null
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        contentContainerStyle={styles.listContentContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No liked businesses found.</Text>
          </View>
        }
      />
    </View>
  );
}
