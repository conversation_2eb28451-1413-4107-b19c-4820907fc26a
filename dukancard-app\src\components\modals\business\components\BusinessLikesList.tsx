import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  Image,
  RefreshControl,
  TouchableOpacity,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import {
  fetchBusinessLikesReceived,
  BusinessLikeReceived,
} from "@/backend/supabase/services/business/businessSocialService";
import { createBusinessLikesModalStyles } from "@/styles/modals/business/business-likes-modal";
import { BusinessLikesModalSkeleton } from "@/src/components/skeletons/modals/BusinessLikesModalSkeleton";
import { COLUMNS } from "@/src/config/supabase/constants";

// Define interfaces for the expected data structure
interface BusinessProfileDataForLike {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface CustomerProfileDataForLike {
  id: string;
  name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface BusinessLikesListProps {
  businessId: string;
  searchTerm: string;
}

export default function BusinessLikesList({
  businessId,
  searchTerm,
}: BusinessLikesListProps) {
  const theme = useTheme();
  const styles = createBusinessLikesModalStyles(theme);
  const [likes, setLikes] = useState<BusinessLikeReceived[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchLikes = useCallback(
    async (isRefreshing = false) => {
      if (!businessId) return;

      try {
        const currentPage = isRefreshing ? 1 : page;

        if (currentPage === 1 && !isRefreshing) {
          setLoading(true);
        } else if (currentPage > 1) {
          setLoadingMore(true);
        }
        if (isRefreshing) {
          setRefreshing(true);
        }

        const { items, hasMore: newHasMore } = await fetchBusinessLikesReceived(
          businessId,
          currentPage,
          10
        );

        if (isRefreshing || currentPage === 1) {
          setLikes(items);
          setPage(2);
        } else {
          setLikes((prev) => [...prev, ...items]);
          setPage(currentPage + 1);
        }
        setHasMore(newHasMore);
      } catch (error) {
        console.error("Failed to fetch likes:", error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
        if (isRefreshing) {
          setRefreshing(false);
        }
      }
    },
    [businessId, page]
  );

  useEffect(() => {
    setPage(1);
    setLikes([]);
    setHasMore(true);
    fetchLikes(true);
  }, [businessId, searchTerm]);

  useEffect(() => {
    if (page > 1) {
      fetchLikes();
    }
  }, [page]);



  const renderItem = ({ item }: { item: BusinessLikeReceived }) => {
    const profile =
      item[COLUMNS.PROFILE_TYPE] === "business"
        ? item.business_profiles
        : item.customer_profiles;
    const name =
      item[COLUMNS.PROFILE_TYPE] === "business"
        ? (profile as BusinessProfileDataForLike)?.business_name
        : (profile as CustomerProfileDataForLike)?.name;
    const avatarUrl =
      item[COLUMNS.PROFILE_TYPE] === "business"
        ? (profile as any)?.logo_url
        : (profile as any)?.avatar_url;

    return (
      <TouchableOpacity style={styles.likeItem}>
        <Image
          source={{
            uri:
              avatarUrl ||
              "https://asset.brandfetch.io/id235U50sE/idj9kF8hYy.jpeg",
          }}
          style={styles.avatar}
        />
        <View style={styles.likeContent}>
          <Text style={styles.likeName} numberOfLines={1}>
            {name || "Anonymous"}
          </Text>
          <Text style={styles.likeType} numberOfLines={1}>
            {item.profile_type === "business" ? "Business" : "Customer"}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoadingContainer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      fetchLikes();
    }
  };

  const handleRefresh = () => {
    setPage(1);
    fetchLikes(true);
  };

  if (loading) {
    return <BusinessLikesModalSkeleton />;
  }

  return (
    <View style={styles.listContainer}>
      <FlatList
        data={likes}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        contentContainerStyle={styles.listContentContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No likes received yet.</Text>
          </View>
        }
      />
    </View>
  );
}
