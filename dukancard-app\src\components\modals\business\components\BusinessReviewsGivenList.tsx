import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import {
  reviewsService,
  ReviewData,
} from "@/backend/supabase/services/posts/socialService";
import { useAuth } from "@/src/contexts/AuthContext";
import { ReviewCard } from "@/src/components/social/ReviewCard";
import { BusinessReviewsModalSkeleton } from "@/src/components/skeletons/modals/BusinessReviewsModalSkeleton";
import { createBusinessReviewsModalStyles } from "@/styles/modals/business/business-reviews-modal";
import { useTheme } from "@/src/hooks/useTheme";

type SortByType = "newest" | "oldest" | "rating_high" | "rating_low";

interface BusinessReviewsGivenListProps {
  sortBy: SortByType;
  searchTerm?: string;
  onReviewCountChange: (count: number) => void;
}

export default function BusinessReviewsGivenList({ 
  sortBy, 
  searchTerm = "", 
  onReviewCountChange 
}: BusinessReviewsGivenListProps) {
  const { user } = useAuth();
  const theme = useTheme();
  const styles = createBusinessReviewsModalStyles(theme);

  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchReviews = useCallback(
    async (isRefreshing = false) => {
      if (!user) return;

      const currentPage = isRefreshing ? 1 : page;

      if (currentPage === 1 && !isRefreshing) {
        setLoading(true);
      } else if (currentPage > 1) {
        setLoadingMore(true);
      }
      if (isRefreshing) {
        setRefreshing(true);
      }

      try {
        const { items, hasMore: newHasMore, totalCount } = await reviewsService.fetchReviews(
          user.id,
          currentPage,
          10,
          sortBy,
          searchTerm
        );

        if (isRefreshing || currentPage === 1) {
          setReviews(items);
          setPage(2);
        } else {
          setReviews((prevReviews) => [...prevReviews, ...items]);
          setPage(currentPage + 1);
        }
        setHasMore(newHasMore);
        onReviewCountChange(totalCount);
      } catch (error) {
        console.error("Failed to fetch reviews:", error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
        if (isRefreshing) {
          setRefreshing(false);
        }
      }
    },
    [user, page, sortBy, searchTerm, onReviewCountChange]
  );

  useEffect(() => {
    setPage(1);
    setReviews([]);
    setHasMore(true);
    fetchReviews(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, sortBy, searchTerm]);

  useEffect(() => {
    if (page > 1) {
      fetchReviews();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page]);

  const handleRefresh = () => {
    setPage(1);
    fetchReviews(true);
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore) {
      fetchReviews();
    }
  };

  const handleDeleteReview = async (reviewId: string) => {
    try {
      await reviewsService.deleteReview(reviewId);
      setReviews((prevReviews) => prevReviews.filter((review) => review.id !== reviewId));
    } catch (error) {
      console.error("Failed to delete review:", error);
    }
  };

  const handleUpdateReview = async (reviewId: string, rating: number, reviewText: string) => {
    try {
      await reviewsService.updateReview(reviewId, rating, reviewText);
      setReviews((prevReviews) =>
        prevReviews.map((review) =>
          review.id === reviewId
            ? { ...review, rating, review_text: reviewText }
            : review
        )
      );
    } catch (error) {
      console.error("Failed to update review:", error);
    }
  };

  const renderReview = ({ item }: { item: ReviewData }) => (
    <ReviewCard review={item} onDelete={handleDeleteReview} onUpdate={handleUpdateReview} />
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoadingContainer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        No reviews given yet. When you review businesses, they&apos;ll appear here.
      </Text>
    </View>
  );

  if (loading) {
    return <BusinessReviewsModalSkeleton />;
  }

  return (
    <View style={styles.listContainer}>
      <FlatList
        data={reviews}
        renderItem={renderReview}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}
