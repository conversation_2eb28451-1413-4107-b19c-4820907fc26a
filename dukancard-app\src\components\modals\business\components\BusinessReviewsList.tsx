import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import {
  businessSocialService,
  BusinessReviewReceived,
} from "@/backend/supabase/services/business/businessSocialService";
import { useAuth } from "@/src/contexts/AuthContext";
import { BusinessReviewsModalSkeleton } from "@/src/components/skeletons/modals/BusinessReviewsModalSkeleton";
import { createBusinessReviewsModalStyles } from "@/styles/modals/business/business-reviews-modal";
import { useTheme } from "@/src/hooks/useTheme";

type SortByType = "newest" | "oldest" | "rating_high" | "rating_low";

interface BusinessReviewsListProps {
  businessId: string;
  sortBy: SortByType;
  searchTerm?: string;
  onReviewCountChange?: (count: number) => void;
}

interface BusinessReviewCardProps {
  review: BusinessReviewReceived;
}

const BusinessReviewCard: React.FC<BusinessReviewCardProps> = ({ review }) => {
  const theme = useTheme();
  const styles = createBusinessReviewsModalStyles(theme);

  const getProfileName = () => {
    if (review.profile_type === "customer" && review.customer_profiles) {
      return review.customer_profiles.name || "Anonymous Customer";
    } else if (review.profile_type === "business" && review.business_profiles) {
      return review.business_profiles.business_name || "Anonymous Business";
    }
    return "Anonymous";
  };

  const getProfileAvatar = () => {
    if (review.profile_type === "customer" && review.customer_profiles) {
      return review.customer_profiles.avatar_url;
    } else if (review.profile_type === "business" && review.business_profiles) {
      return review.business_profiles.logo_url;
    }
    return null;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Text
        key={index}
        style={{
          color: index < rating ? "#FFD700" : theme.colors.border,
          fontSize: 16,
        }}
      >
        ★
      </Text>
    ));
  };

  return (
    <View style={styles.reviewCard}>
      <View style={styles.reviewHeader}>
        <View style={styles.reviewerInfo}>
          <View style={styles.reviewerAvatar}>
            <Text style={styles.reviewerAvatarText}>
              {getProfileName().charAt(0).toUpperCase()}
            </Text>
          </View>
          <View style={styles.reviewerDetails}>
            <Text style={styles.reviewerName}>{getProfileName()}</Text>
            <Text style={styles.reviewDate}>{formatDate(review.created_at)}</Text>
          </View>
        </View>
        <View style={styles.ratingContainer}>
          <View style={styles.starsContainer}>{renderStars(review.rating)}</View>
        </View>
      </View>
      {review.review_text && (
        <Text style={styles.reviewText}>{review.review_text}</Text>
      )}
    </View>
  );
};

export default function BusinessReviewsList({
  businessId,
  sortBy,
  searchTerm = "",
  onReviewCountChange,
}: BusinessReviewsListProps) {
  const theme = useTheme();
  const styles = createBusinessReviewsModalStyles(theme);
  const { user } = useAuth();

  const [reviews, setReviews] = useState<BusinessReviewReceived[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const fetchReviews = useCallback(
    async (page: number = 1, isRefresh: boolean = false) => {
      if (!businessId) return;

      try {
        if (page === 1) {
          isRefresh ? setRefreshing(true) : setLoading(true);
        } else {
          setLoadingMore(true);
        }

        const result = await businessSocialService.fetchBusinessReviewsReceived(
          businessId,
          page,
          10,
          sortBy
        );

        if (page === 1) {
          setReviews(result.items);
          setCurrentPage(2);
        } else {
          setReviews((prev) => [...prev, ...result.items]);
          setCurrentPage(page + 1);
        }

        setHasMore(result.hasMore);
        setTotalCount(result.totalCount);

        if (onReviewCountChange) {
          onReviewCountChange(result.totalCount);
        }
      } catch (error) {
        console.error("Error fetching business reviews:", error);
      } finally {
        setLoading(false);
        setRefreshing(false);
        setLoadingMore(false);
      }
    },
    [businessId, sortBy, onReviewCountChange]
  );

  useEffect(() => {
    setCurrentPage(1);
    setReviews([]);
    setHasMore(true);
    fetchReviews(1);
  }, [businessId, sortBy, searchTerm]);

  const handleRefresh = () => {
    fetchReviews(1, true);
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      fetchReviews(currentPage + 1);
    }
  };

  const renderReview = ({ item }: { item: BusinessReviewReceived }) => (
    <BusinessReviewCard review={item} />
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoadingContainer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        No reviews received yet. When customers and businesses review your business, they'll appear here.
      </Text>
    </View>
  );

  if (loading) {
    return <BusinessReviewsModalSkeleton />;
  }

  return (
    <View style={styles.listContainer}>
      <FlatList
        data={reviews}
        renderItem={renderReview}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}
