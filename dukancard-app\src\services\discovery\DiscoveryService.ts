/**
 * Centralized Discovery Service
 * Handles all search, filter, and sort operations for the discovery screen
 * Eliminates redundancies and provides consistent behavior
 */

import { supabase } from "../../config/supabase";
import {
  BusinessSortBy,
  BusinessCardData,
  NearbyProduct,
  ProductSortOption,
} from "../../types/discovery";
import { applySorting } from "./utils/secureBusinessProfiles";
import { TABLES, COLUMNS, STATUS } from "../../config/supabase/constants";

// Types
export interface DiscoverySearchParams {
  viewType: "cards" | "products";
  searchTerm?: string | null;
  category?: string | null;
  pincode?: string | null;
  city?: string | null;
  locality?: string | null;
  page?: number;
  limit?: number;
  businessSort?: BusinessSortBy;
  productSort?: ProductSortOption;
  productType?: "physical" | "service" | null;
  userLocation?: { latitude: number; longitude: number };
}

export interface DiscoveryResult {
  businesses?: BusinessCardData[];
  products?: NearbyProduct[];
  totalCount: number;
  hasMore: boolean;
  nextPage: number | null;
  isAuthenticated: boolean;
  location?: { city: string; state: string } | null;
}

export interface NormalizedParams {
  viewType: "cards" | "products";
  searchTerm: string | null;
  category: string | null;
  pincode: string | null;
  city: string | null;
  locality: string | null;
  page: number;
  limit: number;
  businessSort: BusinessSortBy;
  productSort: ProductSortOption;
  productType: "physical" | "service" | null;
}

/**
 * Centralized Discovery Service Class
 */
export class DiscoveryService {
  private supabase = supabase;

  /**
   * Main search method - handles all discovery operations
   */
  async search(params: DiscoverySearchParams): Promise<{
    data?: DiscoveryResult;
    error?: string;
  }> {
    try {
      // Validate and normalize parameters
      const normalizedParams = this.validateAndNormalizeParams(params);

      this.logOperation("search", normalizedParams);

      // Route to appropriate search method based on view type
      if (normalizedParams.viewType === "products") {
        return await this.searchProducts(normalizedParams);
      } else {
        return await this.searchBusinesses(normalizedParams);
      }
    } catch (error) {
      console.error("DiscoveryService.search error:", error);
      return { error: "An unexpected error occurred during search" };
    }
  }

  /**
   * Search for products
   */
  private async searchProducts(params: NormalizedParams): Promise<{
    data?: DiscoveryResult;
    error?: string;
  }> {
    try {
      const offset = (params.page - 1) * params.limit;

      // Step 1: Get valid business IDs based on filters
      const validBusinessIds = await this.getValidBusinessIds(params);

      if (validBusinessIds.length === 0) {
        return {
          data: {
            products: [],
            totalCount: 0,
            hasMore: false,
            nextPage: null,
            isAuthenticated: true,
          },
        };
      }

      // Step 2: Count total products
      const totalCount = await this.countProducts(validBusinessIds, params);

      // Step 3: Fetch products
      const products = await this.fetchProducts(
        validBusinessIds,
        params,
        offset
      );

      // Step 4: Calculate pagination
      const hasMore = totalCount > offset + products.length;
      const nextPage = hasMore ? params.page + 1 : null;

      return {
        data: {
          products,
          totalCount,
          hasMore,
          nextPage,
          isAuthenticated: true,
        },
      };
    } catch (error) {
      console.error("DiscoveryService.searchProducts error:", error);
      return { error: "Failed to search products" };
    }
  }

  /**
   * Search for businesses
   */
  private async searchBusinesses(params: NormalizedParams): Promise<{
    data?: DiscoveryResult;
    error?: string;
  }> {
    try {
      const offset = (params.page - 1) * params.limit;

      // Build business query with all filters
      let countQuery = this.supabase
        .from(TABLES.BUSINESS_PROFILES)
        .select(COLUMNS.ID, { count: "exact" })
        .eq(COLUMNS.STATUS, STATUS.ONLINE);

      let businessQuery = this.supabase
        .from(TABLES.BUSINESS_PROFILES)
        .select(
          `
          ${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.LOGO_URL}, ${COLUMNS.MEMBER_NAME}, ${COLUMNS.TITLE},
          ${COLUMNS.ADDRESS_LINE}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.LOCALITY}, ${COLUMNS.PHONE}, ${COLUMNS.INSTAGRAM_URL},
          ${COLUMNS.FACEBOOK_URL}, ${COLUMNS.WHATSAPP_NUMBER}, ${COLUMNS.ABOUT_BIO}, ${COLUMNS.STATUS}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.THEME_COLOR},
          ${COLUMNS.DELIVERY_INFO}, ${COLUMNS.TOTAL_LIKES}, ${COLUMNS.TOTAL_SUBSCRIPTIONS}, ${COLUMNS.AVERAGE_RATING}, ${COLUMNS.BUSINESS_HOURS},
          ${COLUMNS.BUSINESS_CATEGORY}, ${COLUMNS.TRIAL_END_DATE}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.CONTACT_EMAIL}, ${COLUMNS.ESTABLISHED_YEAR},
          ${COLUMNS.CUSTOM_BRANDING}, ${COLUMNS.CUSTOM_ADS}, ${COLUMNS.LATITUDE}, ${COLUMNS.LONGITUDE}
        `
        )
        .eq(COLUMNS.STATUS, STATUS.ONLINE);

      // Apply all filters
      [countQuery, businessQuery] = this.applyBusinessFilters(
        countQuery,
        businessQuery,
        params
      );

      // Apply sorting and pagination to business query
      businessQuery = applySorting(businessQuery, params.businessSort);
      businessQuery = businessQuery.range(offset, offset + params.limit - 1);

      // Execute queries
      const [countResult, dataResult] = await Promise.all([
        countQuery,
        businessQuery,
      ]);

      if (countResult.error) {
        console.error("Business count query error:", countResult.error);
        return { error: "Failed to count businesses" };
      }

      if (dataResult.error) {
        console.error("Business data query error:", dataResult.error);
        return { error: "Failed to fetch businesses" };
      }

      // Process results
      const totalCount = countResult.count || 0;
      const businesses = this.processBusinessData(dataResult.data || []);
      const hasMore = totalCount > offset + businesses.length;
      const nextPage = hasMore ? params.page + 1 : null;

      return {
        data: {
          businesses,
          totalCount,
          hasMore,
          nextPage,
          isAuthenticated: true,
        },
      };
    } catch (error) {
      console.error("DiscoveryService.searchBusinesses error:", error);
      return { error: "Failed to search businesses" };
    }
  }

  /**
   * Get valid business IDs based on location and category filters
   */
  private async getValidBusinessIds(
    params: NormalizedParams
  ): Promise<string[]> {
    console.log("🔍 getValidBusinessIds called with filters:", {
      city: params.city,
      pincode: params.pincode,
      locality: params.locality,
      category: params.category,
    });

    let businessQuery = this.supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.STATUS, STATUS.ONLINE);

    // Apply location filters
    if (params.city) {
      businessQuery = businessQuery.eq(COLUMNS.CITY, params.city);
      console.log("📍 Applied city filter:", params.city);
    }
    if (params.pincode) {
      businessQuery = businessQuery.eq(COLUMNS.PINCODE, params.pincode);
      console.log("📍 Applied pincode filter:", params.pincode);
    }
    if (params.locality) {
      businessQuery = businessQuery.eq(COLUMNS.LOCALITY, params.locality);
      console.log("📍 Applied locality filter:", params.locality);
    }

    // Apply category filter - this is crucial for category filtering to work
    if (params.category) {
      businessQuery = businessQuery.eq("business_category", params.category);
      console.log("🏷️ Applied category filter:", params.category);
    }

    const { data, error } = await businessQuery;

    if (error) {
      console.error("❌ Error fetching valid business IDs:", error);
      return [];
    }

    const businessIds = data?.map((b: any) => b.id) || [];
    console.log("✅ Found valid business IDs:", {
      count: businessIds.length,
      ids: businessIds.slice(0, 5), // Log first 5 IDs for debugging
    });

    return businessIds;
  }

  /**
   * Count total products from valid businesses
   */
  private async countProducts(
    validBusinessIds: string[],
    params: NormalizedParams
  ): Promise<number> {
    let countQuery = this.supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select(COLUMNS.ID, { count: "exact" })
      .in(COLUMNS.BUSINESS_ID, validBusinessIds)
      .eq(COLUMNS.IS_AVAILABLE, true);

    // Apply product-specific filters
    if (params.productType) {
      countQuery = countQuery.eq(COLUMNS.PRODUCT_TYPE, params.productType);
    }
    if (params.searchTerm) {
      countQuery = countQuery.ilike(COLUMNS.NAME, `%${params.searchTerm}%`);
    }

    const { count, error } = await countQuery;

    if (error) {
      console.error("Error counting products:", error);
      return 0;
    }

    return count || 0;
  }

  /**
   * Fetch products from valid businesses
   */
  private async fetchProducts(
    validBusinessIds: string[],
    params: NormalizedParams,
    offset: number
  ): Promise<NearbyProduct[]> {
    let productsQuery = this.supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select(
        `
        ${COLUMNS.ID}, ${COLUMNS.BUSINESS_ID}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.PRODUCT_TYPE},
        ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG},
        business_profiles!business_id(business_slug, latitude, longitude)
      `
      )
      .in(COLUMNS.BUSINESS_ID, validBusinessIds)
      .eq(COLUMNS.IS_AVAILABLE, true);

    // Apply product-specific filters
    if (params.productType) {
      productsQuery = productsQuery.eq(COLUMNS.PRODUCT_TYPE, params.productType);
    }
    if (params.searchTerm) {
      productsQuery = productsQuery.ilike(COLUMNS.NAME, `%${params.searchTerm}%`);
    }

    // Apply sorting
    productsQuery = this.applyProductSorting(productsQuery, params.productSort);

    // Apply pagination
    productsQuery = productsQuery.range(offset, offset + params.limit - 1);

    const { data, error } = await productsQuery;

    if (error) {
      console.error("Error fetching products:", error);
      return [];
    }

    return this.processProductData(data || []);
  }

  /**
   * Apply filters to business queries
   */
  private applyBusinessFilters(
    countQuery: any,
    businessQuery: any,
    params: NormalizedParams
  ): [any, any] {
    // Search term filter
    if (params.searchTerm) {
      countQuery = countQuery.ilike(COLUMNS.BUSINESS_NAME, `%${params.searchTerm}%`);
      businessQuery = businessQuery.ilike(
        COLUMNS.BUSINESS_NAME,
        `%${params.searchTerm}%`
      );
    }

    // Location filters
    if (params.city) {
      countQuery = countQuery.eq(COLUMNS.CITY, params.city);
      businessQuery = businessQuery.eq(COLUMNS.CITY, params.city);
    }
    if (params.pincode) {
      countQuery = countQuery.eq(COLUMNS.PINCODE, params.pincode);
      businessQuery = businessQuery.eq(COLUMNS.PINCODE, params.pincode);
    }
    if (params.locality) {
      countQuery = countQuery.eq(COLUMNS.LOCALITY, params.locality);
      businessQuery = businessQuery.eq(COLUMNS.LOCALITY, params.locality);
    }

    // Category filter - crucial for category filtering
    if (params.category) {
      countQuery = countQuery.eq(COLUMNS.BUSINESS_CATEGORY, params.category);
      businessQuery = businessQuery.eq(COLUMNS.BUSINESS_CATEGORY, params.category);
    }

    return [countQuery, businessQuery];
  }

  /**
   * Apply sorting to product queries
   */
  private applyProductSorting(query: any, sortBy: ProductSortOption): any {
    switch (sortBy) {
      case "newest":
        return query.order(COLUMNS.CREATED_AT, { ascending: false });
      case "name_asc":
        return query.order(COLUMNS.NAME, { ascending: true });
      case "name_desc":
        return query.order(COLUMNS.NAME, { ascending: false });
      case "price_low":
        return query.order(COLUMNS.BASE_PRICE, { ascending: true });
      case "price_high":
        return query.order(COLUMNS.BASE_PRICE, { ascending: false });
      default:
        return query.order(COLUMNS.CREATED_AT, { ascending: false });
    }
  }

  /**
   * Process raw business data into BusinessCardData format
   */
  private processBusinessData(rawData: any[]): BusinessCardData[] {
    return rawData.map((data) => ({
      id: data.id,
      business_name: data[COLUMNS.BUSINESS_NAME] ?? "",
      contact_email: data[COLUMNS.CONTACT_EMAIL] ?? "",
      has_active_subscription: true, // Simplified for React Native
      trial_end_date: data[COLUMNS.TRIAL_END_DATE] ?? null,
      created_at: data[COLUMNS.CREATED_AT] ?? undefined,
      updated_at: data[COLUMNS.UPDATED_AT] ?? undefined,
      logo_url: data[COLUMNS.LOGO_URL] ?? "",
      member_name: data[COLUMNS.MEMBER_NAME] ?? "",
      title: data[COLUMNS.TITLE] ?? "",
      address_line: data[COLUMNS.ADDRESS_LINE] ?? "",
      city: data[COLUMNS.CITY] ?? "",
      state: data[COLUMNS.STATE] ?? "",
      pincode: data[COLUMNS.PINCODE] ?? "",
      locality: data[COLUMNS.LOCALITY] ?? "",
      phone: data[COLUMNS.PHONE] ?? "",
      instagram_url: data[COLUMNS.INSTAGRAM_URL] ?? "",
      facebook_url: data[COLUMNS.FACEBOOK_URL] ?? "",
      whatsapp_number: data[COLUMNS.WHATSAPP_NUMBER] ?? "",
      about_bio: data[COLUMNS.ABOUT_BIO] ?? "",
      status: data[COLUMNS.STATUS] ?? STATUS.ONLINE,
      business_slug: data[COLUMNS.BUSINESS_SLUG] ?? "",
      theme_color: data[COLUMNS.THEME_COLOR] ?? "",
      delivery_info: data[COLUMNS.DELIVERY_INFO] ?? "",
      business_hours: data[COLUMNS.BUSINESS_HOURS] ?? "",
      business_category: data[COLUMNS.BUSINESS_CATEGORY] ?? "",
      total_likes: data[COLUMNS.TOTAL_LIKES] ?? 0,
      total_subscriptions: data[COLUMNS.TOTAL_SUBSCRIPTIONS] ?? 0,
      average_rating: data[COLUMNS.AVERAGE_RATING] ?? 0,
      established_year: data[COLUMNS.ESTABLISHED_YEAR] ?? null,
      website_url: "",
      linkedin_url: "",
      twitter_url: "",
      youtube_url: "",
      call_number: "",
      total_visits: 0,
      today_visits: 0,
      yesterday_visits: 0,
      visits_7_days: 0,
      visits_30_days: 0,
      city_slug: null,
      state_slug: null,
      locality_slug: null,
      gallery: null,
      latitude: data[COLUMNS.LATITUDE] ?? null,
      longitude: data[COLUMNS.LONGITUDE] ?? null,
      google_maps_url: data.google_maps_url ?? null,
      custom_branding: data[COLUMNS.CUSTOM_BRANDING] ?? null,
      custom_ads: data[COLUMNS.CUSTOM_ADS] ?? null,
    }));
  }

  /**
   * Process raw product data into NearbyProduct format
   */
  private processProductData(rawData: any[]): NearbyProduct[] {
    return rawData.map((product) => ({
      id: product.id,
      business_id: product.business_id,
      name: product[COLUMNS.NAME] || "",
      description: product[COLUMNS.DESCRIPTION],
      base_price: product[COLUMNS.BASE_PRICE],
      discounted_price: product[COLUMNS.DISCOUNTED_PRICE],
      product_type: product[COLUMNS.PRODUCT_TYPE],
      is_available: product[COLUMNS.IS_AVAILABLE],
      image_url: product[COLUMNS.IMAGE_URL],
      created_at: product[COLUMNS.CREATED_AT],
      updated_at: product[COLUMNS.UPDATED_AT],
      slug: product[COLUMNS.SLUG],
      images: product[COLUMNS.IMAGES] || null,
      featured_image_index: product.featured_image_index || null,
      business_slug: product.business_profiles?.business_slug || "",
      businessLatitude: product.business_profiles?.latitude || null,
      businessLongitude: product.business_profiles?.longitude || null,
    }));
  }

  /**
   * Validate and normalize input parameters
   */
  private validateAndNormalizeParams(
    params: DiscoverySearchParams
  ): NormalizedParams {
    return {
      viewType: params.viewType || "products",
      searchTerm: this.normalizeString(params.searchTerm),
      category: this.normalizeString(params.category),
      pincode: this.normalizeString(params.pincode),
      city: this.normalizeString(params.city),
      locality: this.normalizeString(params.locality),
      page: Math.max(1, params.page || 1),
      limit: Math.min(50, Math.max(1, params.limit || 20)),
      businessSort: params.businessSort || "created_desc",
      productSort: params.productSort || "newest",
      productType: params.productType || null,
    };
  }

  /**
   * Normalize string parameters
   */
  private normalizeString(value: string | null | undefined): string | null {
    if (!value || typeof value !== "string") return null;
    const trimmed = value.trim();
    return trimmed.length > 0 ? trimmed : null;
  }

  /**
   * Log operations for debugging
   */
  private logOperation(operation: string, params: any): void {
    console.log(`DiscoveryService.${operation}:`, {
      viewType: params.viewType,
      searchTerm: params.searchTerm,
      category: params.category,
      location: {
        city: params.city,
        pincode: params.pincode,
        locality: params.locality,
      },
      pagination: {
        page: params.page,
        limit: params.limit,
      },
      sorting: {
        businessSort: params.businessSort,
        productSort: params.productSort,
      },
    });
  }
}

// Export singleton instance
export const discoveryService = new DiscoveryService();
