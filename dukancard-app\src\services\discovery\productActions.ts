import { supabase } from "../../config/supabase";
import {
  BusinessSortBy,
  NearbyProduct,
  getSortingColumn,
  getSortingDirection,
} from "./types";
import { TABLES, COLUMNS, STATUS } from "../../config/supabase/constants";

// Helper function to convert any product result to NearbyProduct
function convertToNearbyProduct(
  product: Record<string, unknown>
): NearbyProduct {
  // Extract business_slug and location data from the joined business_profiles
  let business_slug = null;
  let businessLatitude = null;
  let businessLongitude = null;

  if (product.business_profiles) {
    // Handle both object and array formats
    if (Array.isArray(product.business_profiles)) {
      const businessProfile = product.business_profiles[0];
      business_slug = businessProfile?.business_slug || null;
      businessLatitude = businessProfile?.latitude || null;
      businessLongitude = businessProfile?.longitude || null;
    } else if (
      product.business_profiles &&
      typeof product.business_profiles === "object"
    ) {
      const businessProfile = product.business_profiles as Record<
        string,
        unknown
      >;
      business_slug = (businessProfile.business_slug as string) || null;
      businessLatitude = (businessProfile.latitude as number) || null;
      businessLongitude = (businessProfile.longitude as number) || null;
    }
  }

  return {
    id: product.id as string,
    business_id: (product.business_id as string) || "",
    name: (product.name as string) || "",
    description: (product.description as string) || "",
    base_price: Number(product.base_price) || 0,
    discounted_price: product.discounted_price
      ? Number(product.discounted_price)
      : null,
    product_type:
      (product.product_type as "physical" | "service") || "physical",
    is_available: Boolean(product.is_available) || false,
    image_url: (product.image_url as string) || null,
    created_at: (product.created_at as string) || new Date().toISOString(),
    updated_at: (product.updated_at as string) || new Date().toISOString(),
    slug: product.slug as string | null,
    business_slug: business_slug,
    featured_image_index: 0, // Default value for NearbyProduct
    images: [], // Default empty array for images
    // Add business location data for distance calculation
    businessLatitude: businessLatitude,
    businessLongitude: businessLongitude,
  };
}

// Function to fetch more products with combined search for infinite scroll
export async function fetchMoreProductsCombined(params: {
  businessName?: string | null;
  productName?: string | null;
  pincode?: string | null;
  locality?: string | null;
  city?: string | null;
  page: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productSort?: string;
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  console.log("fetchMoreProductsCombined called with:", params);

  const {
    productName,
    pincode,
    locality,
    city,
    page,
    limit = 20,
    sortBy = "created_desc",
    productSort,
    productType = null,
  } = params;

  // Use the appropriate sort parameter
  const actualSortBy = productSort || sortBy;

  // Use fetchAllProducts directly to avoid circular dependency
  const result = await fetchAllProducts({
    page,
    limit,
    sortBy: actualSortBy as BusinessSortBy,
    productType,
    pincode,
    locality,
    city,
    productName,
    category: null, // fetchMoreProductsCombined doesn't have category parameter
  });

  if (result.error) {
    console.error("fetchMoreProductsCombined error:", result.error);
    return { error: result.error };
  }
  console.log("fetchMoreProductsCombined returning:", result.data);

  if (!result.data?.products) {
    return { error: "No product data found" };
  }

  return {
    data: {
      products: result.data.products,
      totalCount: result.data.totalCount,
      hasMore: result.data.hasMore,
      nextPage: result.data.nextPage,
    },
  };
}

// Function to fetch all products
export async function fetchAllProducts(params: {
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productType?: "physical" | "service" | null;
  pincode?: string | null;
  locality?: string | null;
  city?: string | null;
  productName?: string | null;
  category?: string | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    isAuthenticated: boolean;
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
    pincode = null,
    locality = null,
    city = null,
    productName = null,
    category = null,
  } = params;

  // Authentication is handled by auth guards in React Native
  const isAuthenticated = true;

  try {
    console.log("fetchAllProducts called with:", params);
    const offset = (page - 1) * limit;

    // Get all online business IDs without checking subscription status
    let businessQuery = supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.STATUS, STATUS.ONLINE);

    // Add city filter if provided
    if (city) {
      businessQuery = businessQuery.eq(COLUMNS.CITY, city);
    }

    // Add pincode filter if provided
    if (pincode) {
      businessQuery = businessQuery.eq(COLUMNS.PINCODE, pincode);
    }

    // Add locality filter if provided
    if (locality) {
      businessQuery = businessQuery.eq(COLUMNS.LOCALITY, locality);
    }

    // Add category filter if provided
    if (category && category.trim()) {
      businessQuery = businessQuery.eq(COLUMNS.BUSINESS_CATEGORY, category.trim());
    }

    const { data: validBusinesses, error: businessError } = await businessQuery;

    if (businessError) {
      console.error("Error fetching valid businesses:", businessError);
      return { error: "Failed to fetch valid businesses" };
    }
    console.log("fetchAllProducts valid businesses:", validBusinesses);

    if (!validBusinesses || validBusinesses.length === 0) {
      return {
        data: {
          products: [],
          isAuthenticated,
          totalCount: 0,
          hasMore: false,
          nextPage: null,
        },
      };
    }

    const validBusinessIds = validBusinesses.map((b) => b.id);

    // Build the query for counting products - count products from valid businesses
    let countQuery = supabase
      .from("products_services")
      .select("id", { count: "exact" })
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      countQuery = countQuery.eq("product_type", productType);
    }

    // Add product name filter if provided
    if (productName && productName.trim().length > 0) {
      countQuery = countQuery.ilike("name", `%${productName.trim()}%`);
    }

    // Get total count
    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Error counting products:", countError);
      return { error: "Failed to count products" };
    }
    console.log("fetchAllProducts count:", count);

    // Build the query for fetching products from valid businesses
    let productsQuery = supabase
      .from("products_services")
      .select(
        `
        id, business_id, name, description, base_price, discounted_price, product_type,
        is_available, image_url, created_at, updated_at, slug,
        business_profiles!business_id(business_slug, latitude, longitude)
      `
      )
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      productsQuery = productsQuery.eq("product_type", productType);
    }

    // Add product name filter if provided
    if (productName && productName.trim().length > 0) {
      productsQuery = productsQuery.ilike("name", `%${productName.trim()}%`);
    }

    // Add sorting
    const sortColumn = getSortingColumn(sortBy, true); // true indicates product view
    const sortAscending = getSortingDirection(sortBy);

    // Special handling for price sorting to use discounted_price when available, otherwise base_price
    if (sortColumn === "price") {
      if (sortAscending) {
        productsQuery = productsQuery
          .order("discounted_price", { ascending: true, nullsFirst: false })
          .order("base_price", { ascending: true, nullsFirst: false });
      } else {
        productsQuery = productsQuery
          .order("discounted_price", { ascending: false, nullsFirst: false })
          .order("base_price", { ascending: false, nullsFirst: false });
      }
    } else {
      productsQuery = productsQuery.order(sortColumn, {
        ascending: sortAscending,
      });
    }

    // Add pagination
    productsQuery = productsQuery.range(offset, offset + limit - 1);

    // Execute the query
    const { data: productsData, error: productsError } = await productsQuery;

    if (productsError) {
      console.error("Error fetching products:", productsError);
      return { error: "Failed to fetch products" };
    }
    console.log("fetchAllProducts products data:", productsData);

    // Process the products data to include business_slug
    const products = productsData.map(convertToNearbyProduct);

    // Calculate pagination info
    const totalCount = count || 0;
    const hasMore = totalCount > offset + products.length;
    const nextPage = hasMore ? page + 1 : null;

    const result = {
      data: {
        products,
        isAuthenticated,
        totalCount,
        hasMore,
        nextPage,
      },
    };
    console.log("fetchAllProducts returning:", result);
    return result;
  } catch (error) {
    console.error("Unexpected error in fetchAllProducts:", error);
    return { error: "An unexpected error occurred" };
  }
}

// Function to fetch products by business IDs
export async function fetchProductsByBusinessIds(params: {
  businessIds: string[];
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    isAuthenticated: boolean;
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    businessIds,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
  } = params;

  if (!businessIds || businessIds.length === 0) {
    return {
      error: "No business IDs provided",
    };
  }

  // Authentication is handled by auth guards in React Native
  const isAuthenticated = true;

  try {
    console.log("fetchProductsByBusinessIds called with:", params);
    const offset = (page - 1) * limit;

    // Filter the business IDs to only include online ones
    const { data: validBusinesses, error: businessError } = await supabase
      .from("business_profiles")
      .select("id")
      .in("id", businessIds)
      .eq("status", "online");

    if (businessError) {
      console.error("Error filtering valid businesses:", businessError);
      return { error: "Failed to filter valid businesses" };
    }
    console.log(
      "fetchProductsByBusinessIds valid businesses:",
      validBusinesses
    );

    // If no valid businesses found, return empty result
    if (!validBusinesses || validBusinesses.length === 0) {
      return {
        data: {
          products: [],
          isAuthenticated,
          totalCount: 0,
          hasMore: false,
          nextPage: null,
        },
      };
    }

    // Get the IDs of valid businesses
    const validBusinessIds = validBusinesses.map((b) => b.id);

    // Build the query for counting products
    let countQuery = supabase
      .from("products_services")
      .select("id", { count: "exact" })
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      countQuery = countQuery.eq("product_type", productType);
    }

    // Get total count
    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Error counting products:", countError);
      return { error: "Failed to count products" };
    }
    console.log("fetchProductsByBusinessIds count:", count);

    // Build the query for fetching products
    let productsQuery = supabase
      .from("products_services")
      .select(
        `
        id, business_id, name, description, base_price, discounted_price, product_type,
        is_available, image_url, created_at, updated_at, slug,
        business_profiles!business_id(business_slug, latitude, longitude)
      `
      )
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      productsQuery = productsQuery.eq("product_type", productType);
    }

    // Add sorting
    const sortColumn = getSortingColumn(sortBy, true); // true indicates product view
    const sortAscending = getSortingDirection(sortBy);

    // Special handling for price sorting to use discounted_price when available, otherwise base_price
    if (sortColumn === "price") {
      if (sortAscending) {
        productsQuery = productsQuery
          .order("discounted_price", { ascending: true, nullsFirst: false })
          .order("base_price", { ascending: true, nullsFirst: false });
      } else {
        productsQuery = productsQuery
          .order("discounted_price", { ascending: false, nullsFirst: false })
          .order("base_price", { ascending: false, nullsFirst: false });
      }
    } else {
      productsQuery = productsQuery.order(sortColumn, {
        ascending: sortAscending,
      });
    }

    // Add pagination
    productsQuery = productsQuery.range(offset, offset + limit - 1);

    // Execute the query
    const { data: productsData, error: productsError } = await productsQuery;

    if (productsError) {
      console.error("Error fetching products:", productsError);
      return { error: "Failed to fetch products" };
    }
    console.log("fetchProductsByBusinessIds products data:", productsData);

    // Process the products data to include business_slug
    const products = productsData.map(convertToNearbyProduct);

    // Calculate pagination info
    const totalCount = count || 0;
    const hasMore = totalCount > offset + products.length;
    const nextPage = hasMore ? page + 1 : null;

    const result = {
      data: {
        products,
        isAuthenticated,
        totalCount,
        hasMore,
        nextPage,
      },
    };
    return result;
  } catch (error) {
    console.error("Unexpected error in fetchProductsByBusinessIds:", error);
    return { error: "An unexpected error occurred" };
  }
}
