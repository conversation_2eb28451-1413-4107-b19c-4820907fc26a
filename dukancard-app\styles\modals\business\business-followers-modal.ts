import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createBusinessFollowersModalStyles = (theme: ReturnType<typeof useTheme>) => {
  const { colors, spacing, borderRadius, typography, isDark } = theme;

  return StyleSheet.create({
    // Modal container
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },

    // Header
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
    },
    closeButton: {
      padding: spacing.xs,
    },

    // Content container
    contentContainer: {
      flex: 1,
    },

    // Toggle container
    toggleContainer: {
      flexDirection: "row",
      marginHorizontal: spacing.md,
      marginBottom: spacing.md,
      backgroundColor: colors.card,
      borderRadius: borderRadius.md,
      padding: spacing.xs,
    },
    toggleButton: {
      flex: 1,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borderRadius.sm,
      alignItems: "center",
    },
    toggleButtonActive: {
      backgroundColor: colors.primary,
    },
    toggleButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: "500",
      color: colors.textSecondary,
    },
    toggleButtonTextActive: {
      color: colors.primaryForeground,
    },

    // Search container
    searchContainer: {
      paddingHorizontal: spacing.md,
      marginBottom: spacing.md,
    },
    searchInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDark ? colors.input : colors.card,
      borderRadius: borderRadius.md,
      paddingHorizontal: spacing.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    searchInput: {
      flex: 1,
      height: 48,
      color: colors.foreground,
      fontSize: typography.fontSize.base,
    },

    // List container
    listContainer: {
      flex: 1,
      paddingHorizontal: spacing.md,
    },

    // Empty state
    emptyContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      padding: spacing.xl,
    },
    emptyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
      textAlign: "center",
      marginBottom: spacing.sm,
    },
    emptyText: {
      fontSize: typography.fontSize.base,
      color: colors.textSecondary,
      textAlign: "center",
      marginTop: spacing.md,
    },
    loadingContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    footerLoadingContainer: {
      paddingVertical: spacing.lg,
    },

    // Error state
    errorContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      padding: spacing.xl,
    },
    errorText: {
      fontSize: typography.fontSize.base,
      color: colors.textSecondary,
      textAlign: "center",
      marginBottom: spacing.md,
    },
    retryButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      borderRadius: borderRadius.md,
    },
    retryButtonText: {
      color: colors.primaryForeground,
      fontSize: typography.fontSize.base,
      fontWeight: "600",
    },

    // List styles
    listContentContainer: {
      paddingBottom: spacing.lg,
    },

    // Follower item styles
    followerItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: spacing.sm,
      minHeight: 72,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? "#333" : "#f0f0f0",
    },
    avatar: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: colors.card,
    },
    followerContent: {
      flex: 1,
      marginLeft: spacing.md,
    },
    followerName: {
      fontSize: typography.fontSize.base,
      fontWeight: "600",
      color: colors.foreground,
      marginBottom: spacing.xs,
    },
    followerType: {
      fontSize: typography.fontSize.sm,
      color: colors.textSecondary,
    },
  });
};
