import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createBusinessLikesModalStyles = (theme: ReturnType<typeof useTheme>) => {
  const { isDark } = theme;
  return StyleSheet.create({
    modalContainer: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    contentContainer: {
      flex: 1,
      padding: theme.spacing.md,
    },

    searchContainer: {
      marginBottom: theme.spacing.md,
    },
    listContainer: {
      flex: 1,
    },

    // Toggle container
    toggleContainer: {
      flexDirection: "row",
      marginHorizontal: theme.spacing.md,
      marginBottom: theme.spacing.md,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.xs,
    },
    toggleButton: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
      alignItems: "center",
    },
    toggleButtonActive: {
      backgroundColor: theme.colors.primary,
    },
    toggleButtonText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "500",
      color: theme.colors.textSecondary,
    },
    toggleButtonTextActive: {
      color: theme.colors.primaryForeground,
    },

    // Empty state
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: theme.spacing.lg,
    },
    emptyText: {
      fontSize: theme.typography.fontSize.base,
      color: isDark ? "#999" : "#666",
      textAlign: "center",
    },

    // Loading state
    loadingContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.lg,
    },
    footerLoadingContainer: {
      paddingVertical: theme.spacing.lg,
      alignItems: "center",
    },

    // List styles
    listContentContainer: {
      paddingBottom: theme.spacing.lg,
    },

    // Like item styles
    likeItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      minHeight: 72,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? "#333" : "#f0f0f0",
    },
    avatar: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: theme.colors.card,
    },
    likeContent: {
      flex: 1,
      marginLeft: theme.spacing.md,
    },
    likeName: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginBottom: theme.spacing.xs,
    },
    likeType: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
  });
};
