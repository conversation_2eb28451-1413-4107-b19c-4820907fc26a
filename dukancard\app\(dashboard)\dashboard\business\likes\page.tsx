import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

import { Skeleton } from '@/components/ui/skeleton';
import { Al<PERSON><PERSON>riangle, Heart } from 'lucide-react';
import BusinessLikesPageClient from './components/BusinessLikesPageClient';
import { Suspense } from 'react';
import { LikeListSkeleton } from '@/app/components/shared/likes';

// Import the fetch functions
import { fetchBusinessLikesReceived, fetchBusinessMyLikes } from './actions';

export const metadata: Metadata = {
  title: "Business Likes",
  robots: "noindex, nofollow",
};

export default async function BusinessLikesPage({
  searchParams
}: {
  searchParams: Promise<{ search?: string; page?: string; tab?: string }>
}) {
  // Properly await searchParams
  const { search, page: pageParam, tab } = await searchParams;
  const supabase = await createClient();
  const page = pageParam ? parseInt(pageParam) : 1;
  const searchTerm = search || "";
  const activeTab = tab === 'my-likes' ? 'my-likes' : 'likes-received';

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your likes.');
  }

  try {
    // Fetch data for both tabs
    const [likesReceivedResult, myLikesResult] = await Promise.all([
      fetchBusinessLikesReceived(user.id, activeTab === 'likes-received' ? page : 1, 10),
      fetchBusinessMyLikes(user.id, activeTab === 'my-likes' ? page : 1, 10, activeTab === 'my-likes' ? searchTerm : "")
    ]);

    // Get counts for both tabs
    const [likesReceivedCount, myLikesCount] = await Promise.all([
      fetchBusinessLikesReceived(user.id, 1, 1).then(result => result.totalCount),
      fetchBusinessMyLikes(user.id, 1, 1, "").then(result => result.totalCount)
    ]);

    return (
      <div className="space-y-8">
        <Suspense fallback={
          <div className="space-y-8">
            {/* Header skeleton */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
              <div className="p-3 rounded-xl bg-muted hidden sm:block">
                <Heart className="w-6 h-6 text-foreground" />
              </div>
              <div className="flex-1">
                <Skeleton className="h-8 w-48 mb-2" />
                <Skeleton className="h-4 w-64" />
              </div>
            </div>

            {/* Tabs skeleton */}
            <div className="flex gap-2 border-b border-border">
              <Skeleton className="h-10 w-40 rounded-t-md" />
              <Skeleton className="h-10 w-32 rounded-t-md" />
            </div>

            {/* Search skeleton */}
            <Skeleton className="h-10 w-full rounded-md" />

            {/* Content skeleton */}
            <LikeListSkeleton />
          </div>
        }>
          <BusinessLikesPageClient
            initialLikesReceived={likesReceivedResult.items}
            likesReceivedCount={likesReceivedCount}
            likesReceivedCurrentPage={likesReceivedResult.currentPage}
            initialMyLikes={myLikesResult.items}
            myLikesCount={myLikesCount}
            myLikesCurrentPage={myLikesResult.currentPage}
            searchTerm={searchTerm}
            activeTab={activeTab}
          />
        </Suspense>
      </div>
    );
  } catch (_error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Could not load likes data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }
}
